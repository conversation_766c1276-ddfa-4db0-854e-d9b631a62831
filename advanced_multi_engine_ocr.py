#!/usr/bin/env python3
"""
Advanced Multi-Engine OCR System for RSA Private Key Extraction
Targeting 95% similarity with comprehensive optimization strategies
"""

import os
import sys
import time
import json
import argparse
import threading
import multiprocessing
import re
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from difflib import SequenceMatcher
import psutil
import <PERSON><PERSON>til
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageEnhance, ImageFilter
import pytesseract
import logging
from typing import Dict, List, Any, Tuple

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemMonitor:
    """Monitor system resources and hardware capabilities"""
    
    def __init__(self):
        self.start_time = time.time()
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available and get details"""
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                for gpu in gpus:
                    logger.info(f"GPU Detected: {gpu.name} - Memory: {gpu.memoryTotal}MB")
                return True
            else:
                logger.info("No GPU detected - using CPU only")
                return False
        except Exception as e:
            logger.warning(f"GPU detection failed: {e}")
            return False
    
    def display_hardware_info(self):
        """Display comprehensive hardware information at startup"""
        logger.info("=" * 60)
        logger.info("ADVANCED MULTI-ENGINE OCR SYSTEM")
        logger.info("=" * 60)
        
        # CPU Information
        cpu_count = multiprocessing.cpu_count()
        cpu_freq = psutil.cpu_freq()
        logger.info(f"CPU Cores: {cpu_count}")
        if cpu_freq:
            logger.info(f"CPU Frequency: {cpu_freq.current:.2f} MHz")
        
        # Memory Information
        memory = psutil.virtual_memory()
        logger.info(f"Total RAM: {memory.total / (1024**3):.2f} GB")
        logger.info(f"Available RAM: {memory.available / (1024**3):.2f} GB")
        
        # GPU Information
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                for i, gpu in enumerate(gpus):
                    logger.info(f"GPU {i}: {gpu.name}")
                    logger.info(f"  Memory: {gpu.memoryTotal}MB")
                    logger.info(f"  Load: {gpu.load*100:.1f}%")
            except Exception as e:
                logger.warning(f"GPU info retrieval failed: {e}")
        
        logger.info("=" * 60)

def load_reference_text(reference_file: str) -> str:
    """Load and extract first 4 lines from reference PEM file"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        # Extract first 4 lines as specified
        reference_lines = lines[:4]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first 4 lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts using SequenceMatcher"""
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def extract_first_4_lines(text: str) -> str:
    """Extract first 4 lines from OCR text"""
    lines = text.split('\n')
    first_4_lines = lines[:4]
    return '\n'.join(first_4_lines).strip()

class MonospaceOptimizedPreprocessor:
    """Specialized preprocessing for monospace fonts like Ubuntu Mono"""
    
    @staticmethod
    def optimize_for_ubuntu_mono(img: np.ndarray) -> np.ndarray:
        """Apply preprocessing optimized for Ubuntu Mono font"""
        # Convert to grayscale if needed
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Apply Gaussian blur to reduce noise while preserving character shapes
        blurred = cv2.GaussianBlur(gray, (1, 1), 0)
        
        # Use adaptive threshold optimized for monospace text
        adaptive = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Apply morphological operations to clean up character shapes
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(adaptive, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    @staticmethod
    def enhance_contrast_for_text_editor(img: np.ndarray) -> np.ndarray:
        """Enhance contrast specifically for text editor screenshots"""
        if len(img.shape) == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img.copy()
        
        # Apply CLAHE with parameters optimized for text
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Apply bilateral filter to reduce noise while keeping edges sharp
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # Use Otsu's thresholding
        _, thresh = cv2.threshold(filtered, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return thresh
    
    @staticmethod
    def scale_for_optimal_dpi(img: np.ndarray, target_dpi: int = 300) -> np.ndarray:
        """Scale image to optimal DPI for OCR"""
        # Calculate scaling factor (assuming original is ~96 DPI)
        scale_factor = target_dpi / 96.0
        
        height, width = img.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        # Use INTER_CUBIC for upscaling text
        scaled = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        return scaled

class CharacterPostProcessor:
    """Advanced character-level post-processing for RSA keys"""
    
    # Character substitution rules based on common OCR errors in monospace fonts
    MONOSPACE_CORRECTIONS = {
        # Common OCR misreads in Ubuntu Mono
        'MITI': 'MIII',  # Specific error observed
        'AGEAP': 'AgEAp',  # Case corrections
        'AGEADX': 'AgEApX',  # More specific corrections
        '1': 'I',  # In base64 context, 1 is often I
        '0': 'O',  # In base64 context, 0 is often O
        '5': 'S',  # Common substitution
        '8': 'B',  # Common substitution
        '6': 'G',  # Common substitution
    }
    
    BASE64_CHARS = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=')
    
    @classmethod
    def apply_rsa_key_corrections(cls, text: str) -> str:
        """Apply RSA key specific corrections"""
        lines = text.split('\n')
        corrected_lines = []
        
        for i, line in enumerate(lines):
            if line.startswith('-----'):
                # Keep headers as-is
                corrected_lines.append(line)
            elif i == 1 and line.strip() == '':
                # Keep empty line after header
                corrected_lines.append(line)
            else:
                # Apply corrections to base64 content
                corrected_line = cls._correct_base64_line(line)
                corrected_lines.append(corrected_line)
        
        return '\n'.join(corrected_lines)
    
    @classmethod
    def _correct_base64_line(cls, line: str) -> str:
        """Correct a single base64 line"""
        # Remove spaces first
        cleaned = line.replace(' ', '')
        
        # Apply specific corrections
        for wrong, correct in cls.MONOSPACE_CORRECTIONS.items():
            cleaned = cleaned.replace(wrong, correct)
        
        # Validate and correct individual characters
        corrected_chars = []
        for char in cleaned:
            if char in cls.BASE64_CHARS:
                corrected_chars.append(char)
            else:
                # Try to find the closest valid base64 character
                corrected_char = cls._find_closest_base64_char(char)
                corrected_chars.append(corrected_char)
        
        return ''.join(corrected_chars)
    
    @classmethod
    def _find_closest_base64_char(cls, char: str) -> str:
        """Find the closest valid base64 character"""
        # Common OCR misreads
        char_map = {
            '1': 'I', '|': 'I', 'l': 'I',
            '0': 'O', 'o': 'O',
            '5': 'S', '$': 'S',
            '8': 'B', '6': 'G',
            '2': 'Z', 'z': 'Z',
        }
        
        return char_map.get(char, char)

class MultiEngineOCR:
    """Multi-engine OCR system with ensemble voting"""
    
    def __init__(self):
        self.engines = []
        self._initialize_engines()
    
    def _initialize_engines(self):
        """Initialize available OCR engines"""
        # Tesseract configurations optimized for monospace text
        tesseract_configs = [
            {"name": "Tesseract_PSM6_Mono", "config": "--psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=- "},
            {"name": "Tesseract_PSM8_Mono", "config": "--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=- "},
            {"name": "Tesseract_PSM6_OEM1", "config": "--psm 6 --oem 1"},
            {"name": "Tesseract_PSM6_OEM3", "config": "--psm 6 --oem 3"},
            {"name": "Tesseract_PSM3_Default", "config": "--psm 3"},
        ]
        
        for config in tesseract_configs:
            self.engines.append(("tesseract", config))
        
        # Try to initialize EasyOCR
        try:
            import easyocr
            self.engines.append(("easyocr", {"name": "EasyOCR_EN"}))
            logger.info("EasyOCR initialized successfully")
        except ImportError:
            logger.warning("EasyOCR not available")
        
        # Try to initialize PaddleOCR (CPU mode)
        try:
            from paddleocr import PaddleOCR
            paddle_ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
            self.engines.append(("paddleocr", {"name": "PaddleOCR_CPU", "engine": paddle_ocr}))
            logger.info("PaddleOCR (CPU) initialized successfully")
        except Exception as e:
            logger.warning(f"PaddleOCR not available: {e}")
    
    def extract_with_tesseract(self, img: Image.Image, config: Dict) -> str:
        """Extract text using Tesseract"""
        try:
            text = pytesseract.image_to_string(img, config=config["config"])
            return text.strip()
        except Exception as e:
            logger.error(f"Tesseract extraction failed: {e}")
            return ""
    
    def extract_with_easyocr(self, img_array: np.ndarray) -> str:
        """Extract text using EasyOCR"""
        try:
            import easyocr
            reader = easyocr.Reader(['en'])
            results = reader.readtext(img_array)
            
            # Combine all detected text
            text_parts = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low confidence results
                    text_parts.append(text)
            
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"EasyOCR extraction failed: {e}")
            return ""
    
    def extract_with_paddleocr(self, img_array: np.ndarray, paddle_engine) -> str:
        """Extract text using PaddleOCR"""
        try:
            results = paddle_engine.ocr(img_array, cls=True)
            
            # Extract text from results
            text_parts = []
            for line in results:
                if line:
                    for word_info in line:
                        if len(word_info) >= 2:
                            text = word_info[1][0]
                            confidence = word_info[1][1]
                            if confidence > 0.5:
                                text_parts.append(text)
            
            return '\n'.join(text_parts)
        except Exception as e:
            logger.error(f"PaddleOCR extraction failed: {e}")
            return ""
    
    def extract_with_all_engines(self, image_path: str, preprocessing_method: str = "ubuntu_mono") -> List[Dict]:
        """Extract text using all available engines"""
        results = []
        
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Convert to numpy array for preprocessing
        img_array = np.array(img)
        
        # Apply preprocessing
        preprocessor = MonospaceOptimizedPreprocessor()
        
        if preprocessing_method == "ubuntu_mono":
            processed_array = preprocessor.optimize_for_ubuntu_mono(img_array)
        elif preprocessing_method == "contrast_enhanced":
            processed_array = preprocessor.enhance_contrast_for_text_editor(img_array)
        elif preprocessing_method == "scaled_300dpi":
            processed_array = preprocessor.scale_for_optimal_dpi(img_array, 300)
        else:
            processed_array = img_array
        
        # Convert back to PIL for Tesseract
        processed_img = Image.fromarray(processed_array)
        
        # Test all engines
        for engine_type, config in self.engines:
            try:
                start_time = time.time()
                
                if engine_type == "tesseract":
                    text = self.extract_with_tesseract(processed_img, config)
                elif engine_type == "easyocr":
                    text = self.extract_with_easyocr(processed_array)
                elif engine_type == "paddleocr":
                    text = self.extract_with_paddleocr(processed_array, config["engine"])
                else:
                    continue
                
                processing_time = time.time() - start_time
                
                if text:
                    results.append({
                        "engine": engine_type,
                        "config_name": config["name"],
                        "raw_text": text,
                        "processing_time": processing_time,
                        "preprocessing": preprocessing_method
                    })
                    
            except Exception as e:
                logger.error(f"Engine {engine_type} failed: {e}")
        
        return results

def ensemble_voting(results: List[Dict], reference_text: str) -> Dict:
    """Use ensemble voting to select the best result"""
    if not results:
        return None
    
    # Calculate similarity for each result
    scored_results = []
    for result in results:
        # Extract first 4 lines
        first_4_lines = extract_first_4_lines(result["raw_text"])
        
        # Apply post-processing
        processed_text = CharacterPostProcessor.apply_rsa_key_corrections(first_4_lines)
        
        # Calculate similarity
        similarity = calculate_similarity(processed_text, reference_text)
        
        scored_results.append({
            **result,
            "first_4_lines": first_4_lines,
            "processed_text": processed_text,
            "similarity": similarity
        })
    
    # Sort by similarity
    scored_results.sort(key=lambda x: x["similarity"], reverse=True)
    
    return scored_results[0] if scored_results else None

def main():
    parser = argparse.ArgumentParser(description="Advanced Multi-Engine OCR for RSA Private Key")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--reference", "-r", default="rsa_private_key.pem", 
                       help="Reference file for validation")
    parser.add_argument("--output-dir", "-o", default="ocr_results", 
                       help="Output directory for results")
    parser.add_argument("--target-similarity", "-t", type=float, default=0.95,
                       help="Target similarity threshold (default: 0.95)")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize system monitor
    monitor = SystemMonitor()
    monitor.display_hardware_info()
    
    # Load reference text
    reference_text = load_reference_text(args.reference)
    if not reference_text:
        logger.error("Failed to load reference text. Exiting.")
        return 1
    
    logger.info(f"Target similarity threshold: {args.target_similarity * 100}%")
    
    # Initialize multi-engine OCR
    multi_ocr = MultiEngineOCR()
    logger.info(f"Initialized {len(multi_ocr.engines)} OCR engines")
    
    # Test different preprocessing methods
    preprocessing_methods = ["ubuntu_mono", "contrast_enhanced", "scaled_300dpi", "none"]
    
    all_results = []
    best_result = None
    best_similarity = 0.0
    
    for preprocessing in preprocessing_methods:
        logger.info(f"Testing preprocessing method: {preprocessing}")
        
        # Extract with all engines using this preprocessing
        engine_results = multi_ocr.extract_with_all_engines(args.image_path, preprocessing)
        
        # Find best result for this preprocessing method
        best_for_method = ensemble_voting(engine_results, reference_text)
        
        if best_for_method:
            all_results.extend(engine_results)
            
            logger.info(f"  Best result: {best_for_method['similarity']:.3f} similarity "
                       f"({best_for_method['engine']} - {best_for_method['config_name']})")
            
            if best_for_method['similarity'] > best_similarity:
                best_similarity = best_for_method['similarity']
                best_result = best_for_method
                
            # Check if we've reached the target
            if best_for_method['similarity'] >= args.target_similarity:
                logger.info(f"🎯 TARGET ACHIEVED! {best_for_method['similarity']:.3f} >= {args.target_similarity}")
                break
    
    # Save comprehensive results
    results_file = os.path.join(args.output_dir, 'multi_engine_results.json')
    with open(results_file, 'w') as f:
        # Convert numpy arrays to lists for JSON serialization
        json_results = []
        for result in all_results:
            json_result = {k: v for k, v in result.items() if k != 'engine_object'}
            json_results.append(json_result)
        json.dump(json_results, f, indent=2)
    
    # Display final results
    logger.info("=" * 60)
    logger.info("MULTI-ENGINE OCR RESULTS")
    logger.info("=" * 60)
    
    if best_result:
        logger.info(f"Best similarity achieved: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
        logger.info(f"Best engine: {best_result['engine']} - {best_result['config_name']}")
        logger.info(f"Best preprocessing: {best_result['preprocessing']}")
        
        # Save best result
        best_text_file = os.path.join(args.output_dir, 'best_multi_engine_result.txt')
        with open(best_text_file, 'w') as f:
            f.write(best_result['processed_text'])
        
        # Save comparison
        comparison_file = os.path.join(args.output_dir, 'multi_engine_comparison.txt')
        with open(comparison_file, 'w') as f:
            f.write("REFERENCE TEXT (first 4 lines):\n")
            f.write("=" * 50 + "\n")
            f.write(reference_text + "\n\n")
            f.write("BEST MULTI-ENGINE RESULT:\n")
            f.write("=" * 50 + "\n")
            f.write(best_result['processed_text'] + "\n\n")
            f.write(f"SIMILARITY: {best_result['similarity']:.3f} ({best_result['similarity']*100:.1f}%)\n")
            f.write(f"ENGINE: {best_result['engine']} - {best_result['config_name']}\n")
            f.write(f"PREPROCESSING: {best_result['preprocessing']}\n")
        
        if best_similarity >= args.target_similarity:
            logger.info(f"✅ SUCCESS: Achieved target similarity of {args.target_similarity*100}%!")
            return 0
        else:
            logger.warning(f"⚠️  Target not reached. Best: {best_similarity*100:.1f}% vs Target: {args.target_similarity*100}%")
            return 1
    else:
        logger.error("❌ No successful OCR results obtained!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
