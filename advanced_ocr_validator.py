#!/usr/bin/env python3
"""
Advanced OCR Text Extraction and Validation System
Implements multi-threading, GPU acceleration, comprehensive configuration testing,
and 95% similarity validation against reference text.
"""

import os
import sys
import time
import json
import argparse
import threading
import multiprocessing
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from difflib import SequenceMatcher
import psutil
import GPUtil
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFile
import pytesseract

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True
import logging
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemMonitor:
    """Monitor system resources and hardware capabilities"""
    
    def __init__(self):
        self.start_time = time.time()
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available and get details"""
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                for gpu in gpus:
                    logger.info(f"GPU Detected: {gpu.name} - Memory: {gpu.memoryTotal}MB")
                    logger.info(f"GPU Load: {gpu.load*100:.1f}% - Memory Used: {gpu.memoryUsed}MB")
                return True
            else:
                logger.info("No GPU detected - using CPU only")
                return False
        except Exception as e:
            logger.warning(f"GPU detection failed: {e}")
            return False
    
    def display_hardware_info(self):
        """Display comprehensive hardware information at startup"""
        logger.info("=" * 60)
        logger.info("HARDWARE CAPABILITY DISPLAY")
        logger.info("=" * 60)
        
        # CPU Information
        cpu_count = multiprocessing.cpu_count()
        cpu_freq = psutil.cpu_freq()
        logger.info(f"CPU Cores: {cpu_count}")
        if cpu_freq:
            logger.info(f"CPU Frequency: {cpu_freq.current:.2f} MHz (Max: {cpu_freq.max:.2f} MHz)")
        
        # Memory Information
        memory = psutil.virtual_memory()
        logger.info(f"Total RAM: {memory.total / (1024**3):.2f} GB")
        logger.info(f"Available RAM: {memory.available / (1024**3):.2f} GB")
        
        # GPU Information
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                for i, gpu in enumerate(gpus):
                    logger.info(f"GPU {i}: {gpu.name}")
                    logger.info(f"  Memory: {gpu.memoryTotal}MB (Used: {gpu.memoryUsed}MB)")
                    logger.info(f"  Load: {gpu.load*100:.1f}%")
                    logger.info(f"  Temperature: {gpu.temperature}°C")
            except Exception as e:
                logger.warning(f"GPU info retrieval failed: {e}")
        
        logger.info("=" * 60)
    
    def monitor_resources(self, interval: int = 15):
        """Monitor system resources in real-time"""
        def monitor_loop():
            while True:
                try:
                    # CPU and Memory
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    
                    logger.info(f"[MONITOR] CPU: {cpu_percent:.1f}% | RAM: {memory.percent:.1f}% | "
                              f"Available: {memory.available / (1024**3):.2f}GB")
                    
                    # GPU monitoring if available
                    if self.gpu_available:
                        try:
                            gpus = GPUtil.getGPUs()
                            for i, gpu in enumerate(gpus):
                                logger.info(f"[MONITOR] GPU{i}: {gpu.load*100:.1f}% load | "
                                          f"{gpu.memoryUsed}MB/{gpu.memoryTotal}MB memory | "
                                          f"{gpu.temperature}°C")
                        except Exception as e:
                            logger.warning(f"GPU monitoring failed: {e}")
                    
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Monitoring error: {e}")
                    time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread

class OCRConfigurationMatrix:
    """Comprehensive OCR configuration testing matrix"""
    
    def __init__(self):
        self.successful_configs = []
        self.config_performance = {}
        
    def get_tesseract_configurations(self) -> List[Dict[str, Any]]:
        """Generate comprehensive Tesseract configuration matrix"""
        configs = []
        
        # PSM (Page Segmentation Mode) options
        psm_modes = [3, 6, 7, 8, 11, 12, 13]
        
        # OEM (OCR Engine Mode) options  
        oem_modes = [0, 1, 2, 3]
        
        # Character whitelist for RSA keys
        char_whitelist = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
        
        # Generate all combinations
        for psm in psm_modes:
            for oem in oem_modes:
                configs.append({
                    'name': f'PSM{psm}_OEM{oem}',
                    'config': f'--oem {oem} --psm {psm}',
                    'priority': self._calculate_priority(psm, oem)
                })
                
                # Add whitelist variant
                configs.append({
                    'name': f'PSM{psm}_OEM{oem}_WHITELIST',
                    'config': f'--oem {oem} --psm {psm} -c tessedit_char_whitelist={char_whitelist}',
                    'priority': self._calculate_priority(psm, oem) + 10
                })
        
        # Sort by priority (successful configs first, then by calculated priority)
        configs.sort(key=lambda x: (x['name'] not in [c['name'] for c in self.successful_configs], 
                                   -x['priority']))
        
        return configs
    
    def _calculate_priority(self, psm: int, oem: int) -> int:
        """Calculate configuration priority based on typical success rates"""
        # PSM 6 and 8 typically work well for text blocks
        psm_scores = {6: 10, 8: 9, 7: 8, 3: 7, 13: 6, 11: 5, 12: 4}
        # OEM 3 (default) and 1 (LSTM) typically work best
        oem_scores = {3: 10, 1: 9, 2: 5, 0: 3}
        
        return psm_scores.get(psm, 1) + oem_scores.get(oem, 1)

class ImagePreprocessor:
    """Advanced image preprocessing for OCR optimization"""
    
    @staticmethod
    def preprocess_image(image_path: str, method: str = "default") -> np.ndarray:
        """Apply various preprocessing methods to improve OCR accuracy"""
        try:
            # Load image with PIL first (better format support)
            pil_img = Image.open(image_path)
            if pil_img.mode == 'RGBA':
                pil_img = pil_img.convert('RGB')
            
            # Convert to OpenCV format
            img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
            
            if method == "default":
                return ImagePreprocessor._default_preprocessing(img)
            elif method == "enhanced":
                return ImagePreprocessor._enhanced_preprocessing(img)
            elif method == "aggressive":
                return ImagePreprocessor._aggressive_preprocessing(img)
            else:
                return img
                
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            return None
    
    @staticmethod
    def _default_preprocessing(img: np.ndarray) -> np.ndarray:
        """Default preprocessing pipeline"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Apply threshold
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return thresh
    
    @staticmethod
    def _enhanced_preprocessing(img: np.ndarray) -> np.ndarray:
        """Enhanced preprocessing with contrast adjustment"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Denoise
        denoised = cv2.fastNlMeansDenoising(enhanced)
        
        # Apply adaptive threshold
        thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                     cv2.THRESH_BINARY, 11, 2)
        
        return thresh
    
    @staticmethod
    def _aggressive_preprocessing(img: np.ndarray) -> np.ndarray:
        """Aggressive preprocessing for difficult images"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Multiple denoising passes
        denoised = cv2.bilateralFilter(gray, 9, 75, 75)
        denoised = cv2.fastNlMeansDenoising(denoised)
        
        # Morphological operations
        kernel = np.ones((2,2), np.uint8)
        processed = cv2.morphologyEx(denoised, cv2.MORPH_CLOSE, kernel)
        
        # Sharpen
        kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(processed, -1, kernel_sharpen)
        
        # Final threshold
        _, thresh = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return thresh

class TextSimilarityValidator:
    """Validate OCR results against reference text with 95% similarity threshold"""
    
    @staticmethod
    def calculate_similarity(text1: str, text2: str) -> float:
        """Calculate similarity between two texts using SequenceMatcher"""
        return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()
    
    @staticmethod
    def validate_against_reference(ocr_text: str, reference_text: str, threshold: float = 0.95) -> Dict[str, Any]:
        """Validate OCR text against reference with detailed analysis"""
        similarity = TextSimilarityValidator.calculate_similarity(ocr_text, reference_text)
        
        return {
            'similarity': similarity,
            'passes_threshold': similarity >= threshold,
            'threshold': threshold,
            'ocr_length': len(ocr_text),
            'reference_length': len(reference_text),
            'length_difference': abs(len(ocr_text) - len(reference_text)),
            'ocr_text': ocr_text,
            'reference_text': reference_text
        }

def load_reference_text(reference_file: str) -> str:
    """Load and extract first 4 lines from reference PEM file"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        # Extract first 4 lines as specified
        reference_lines = lines[:4]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first 4 lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def main():
    parser = argparse.ArgumentParser(description="Advanced OCR Text Extraction and Validation")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--reference", "-r", default="rsa_private_key.pem", 
                       help="Reference file for validation")
    parser.add_argument("--output-dir", "-o", default="ocr_results", 
                       help="Output directory for results")
    parser.add_argument("--threads", "-t", type=int, default=multiprocessing.cpu_count(),
                       help="Number of threads for parallel processing")
    parser.add_argument("--similarity-threshold", "-s", type=float, default=0.95,
                       help="Similarity threshold for validation (default: 0.95)")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize system monitor
    monitor = SystemMonitor()
    monitor.display_hardware_info()
    
    # Start resource monitoring
    monitor.monitor_resources(interval=15)
    
    # Load reference text
    reference_text = load_reference_text(args.reference)
    if not reference_text:
        logger.error("Failed to load reference text. Exiting.")
        return 1
    
    logger.info(f"Starting OCR processing with {args.threads} threads")
    logger.info(f"Similarity threshold: {args.similarity_threshold * 100}%")
    
    # Initialize components
    config_matrix = OCRConfigurationMatrix()
    validator = TextSimilarityValidator()

    # Get all configurations
    configurations = config_matrix.get_tesseract_configurations()
    logger.info(f"Testing {len(configurations)} OCR configurations")

    # Results storage
    all_results = []
    best_result = None
    best_similarity = 0.0

    # Progress tracking
    total_configs = len(configurations)
    completed_configs = 0
    start_time = time.time()

    def process_single_config(config_info: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single OCR configuration"""
        config_name = config_info['name']
        config_string = config_info['config']

        result = {
            'config_name': config_name,
            'config_string': config_string,
            'success': False,
            'ocr_text': '',
            'similarity': 0.0,
            'processing_time': 0.0,
            'error': None
        }

        try:
            config_start_time = time.time()

            # Try different preprocessing methods
            preprocessing_methods = ['default', 'enhanced', 'aggressive']

            for method in preprocessing_methods:
                try:
                    # Preprocess image
                    processed_img = ImagePreprocessor.preprocess_image(args.image_path, method)
                    if processed_img is None:
                        continue

                    # Convert to PIL Image for pytesseract
                    pil_img = Image.fromarray(processed_img)

                    # Perform OCR
                    ocr_text = pytesseract.image_to_string(pil_img, config=config_string)

                    if ocr_text.strip():
                        # Validate against reference
                        validation_result = validator.validate_against_reference(
                            ocr_text, reference_text, args.similarity_threshold
                        )

                        result.update({
                            'success': True,
                            'ocr_text': ocr_text.strip(),
                            'similarity': validation_result['similarity'],
                            'passes_threshold': validation_result['passes_threshold'],
                            'preprocessing_method': method,
                            'processing_time': time.time() - config_start_time,
                            'validation_details': validation_result
                        })

                        # If this result passes threshold, we can stop trying other preprocessing methods
                        if validation_result['passes_threshold']:
                            break

                except Exception as e:
                    logger.debug(f"Preprocessing method {method} failed for {config_name}: {e}")
                    continue

        except Exception as e:
            result['error'] = str(e)
            logger.debug(f"Configuration {config_name} failed: {e}")

        return result

    # Process configurations in parallel
    logger.info("Starting parallel OCR processing...")

    with ThreadPoolExecutor(max_workers=args.threads) as executor:
        # Submit all tasks
        future_to_config = {
            executor.submit(process_single_config, config): config
            for config in configurations
        }

        # Process completed tasks
        for future in as_completed(future_to_config):
            completed_configs += 1

            try:
                result = future.result()
                all_results.append(result)

                # Track best result
                if result['success'] and result['similarity'] > best_similarity:
                    best_similarity = result['similarity']
                    best_result = result

                # Progress reporting
                elapsed_time = time.time() - start_time
                progress_percent = (completed_configs / total_configs) * 100
                avg_time_per_config = elapsed_time / completed_configs
                eta = avg_time_per_config * (total_configs - completed_configs)

                logger.info(f"Progress: {completed_configs}/{total_configs} ({progress_percent:.1f}%) | "
                          f"Best similarity: {best_similarity:.3f} | ETA: {eta:.1f}s")

                # Log successful results
                if result['success'] and result['similarity'] >= args.similarity_threshold:
                    logger.info(f"✓ PASSED: {result['config_name']} - "
                              f"Similarity: {result['similarity']:.3f} "
                              f"({result['preprocessing_method']} preprocessing)")

            except Exception as e:
                logger.error(f"Task failed: {e}")

    # Process and save results
    logger.info("=" * 60)
    logger.info("PROCESSING COMPLETE - GENERATING RESULTS")
    logger.info("=" * 60)

    # Sort results by similarity
    all_results.sort(key=lambda x: x['similarity'], reverse=True)

    # Save detailed results
    results_file = os.path.join(args.output_dir, 'detailed_results.json')
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2, default=str)

    logger.info(f"Detailed results saved to: {results_file}")

    # Generate summary report
    successful_results = [r for r in all_results if r['success']]
    passing_results = [r for r in successful_results if r.get('passes_threshold', False)]

    summary = {
        'total_configurations_tested': len(all_results),
        'successful_extractions': len(successful_results),
        'passing_threshold': len(passing_results),
        'best_similarity': best_similarity,
        'processing_time_total': time.time() - start_time,
        'reference_text_length': len(reference_text),
        'similarity_threshold': args.similarity_threshold
    }

    if best_result:
        summary['best_result'] = {
            'config_name': best_result['config_name'],
            'similarity': best_result['similarity'],
            'preprocessing_method': best_result.get('preprocessing_method', 'unknown'),
            'ocr_text_preview': best_result['ocr_text'][:200] + '...' if len(best_result['ocr_text']) > 200 else best_result['ocr_text']
        }

    # Save summary
    summary_file = os.path.join(args.output_dir, 'summary.json')
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)

    # Display results
    logger.info(f"Total configurations tested: {summary['total_configurations_tested']}")
    logger.info(f"Successful extractions: {summary['successful_extractions']}")
    logger.info(f"Passing {args.similarity_threshold*100}% threshold: {summary['passing_threshold']}")
    logger.info(f"Best similarity achieved: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
    logger.info(f"Total processing time: {summary['processing_time_total']:.2f} seconds")

    if best_result:
        logger.info("=" * 60)
        logger.info("BEST RESULT DETAILS")
        logger.info("=" * 60)
        logger.info(f"Configuration: {best_result['config_name']}")
        logger.info(f"Similarity: {best_result['similarity']:.3f} ({best_result['similarity']*100:.1f}%)")
        logger.info(f"Preprocessing: {best_result.get('preprocessing_method', 'unknown')}")
        logger.info(f"Processing time: {best_result['processing_time']:.2f} seconds")
        logger.info(f"Passes threshold: {'YES' if best_result.get('passes_threshold', False) else 'NO'}")

        # Save best result text
        best_text_file = os.path.join(args.output_dir, 'best_ocr_result.txt')
        with open(best_text_file, 'w') as f:
            f.write(best_result['ocr_text'])
        logger.info(f"Best OCR text saved to: {best_text_file}")

        # Save comparison
        comparison_file = os.path.join(args.output_dir, 'comparison.txt')
        with open(comparison_file, 'w') as f:
            f.write("REFERENCE TEXT:\n")
            f.write("=" * 50 + "\n")
            f.write(reference_text + "\n\n")
            f.write("BEST OCR RESULT:\n")
            f.write("=" * 50 + "\n")
            f.write(best_result['ocr_text'] + "\n\n")
            f.write(f"SIMILARITY: {best_result['similarity']:.3f} ({best_result['similarity']*100:.1f}%)\n")
        logger.info(f"Comparison saved to: {comparison_file}")

        # Create annotated image if possible
        try:
            annotated_img_path = os.path.join(args.output_dir, 'annotated_result.png')
            original_img = cv2.imread(args.image_path)
            if original_img is not None:
                # Add text overlay with results
                font = cv2.FONT_HERSHEY_SIMPLEX
                text = f"Best: {best_result['config_name']} | Similarity: {best_result['similarity']:.3f}"
                cv2.putText(original_img, text, (10, 30), font, 0.7, (0, 255, 0), 2)
                cv2.imwrite(annotated_img_path, original_img)
                logger.info(f"Annotated image saved to: {annotated_img_path}")
        except Exception as e:
            logger.warning(f"Could not create annotated image: {e}")

    else:
        logger.warning("No successful OCR results obtained!")
        return 1

    # Final status
    if passing_results:
        logger.info(f"✓ SUCCESS: {len(passing_results)} configuration(s) passed the {args.similarity_threshold*100}% threshold")
        return 0
    else:
        logger.warning(f"✗ NO RESULTS passed the {args.similarity_threshold*100}% threshold")
        logger.info(f"Best similarity achieved was {best_similarity*100:.1f}%")
        return 1

if __name__ == "__main__":
    sys.exit(main())
