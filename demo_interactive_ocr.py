#!/usr/bin/env python3
"""
Demo of Interactive Learning OCR System
Shows how the character-by-character validation works
"""

import os
import sys
from interactive_learning_ocr import LearningDatabase, InteractiveOCR

def demo_learning_system():
    """Demonstrate the learning OCR system"""
    print("🎯 Interactive Learning OCR Demo")
    print("=" * 50)
    
    # Initialize the learning database
    learning_db = LearningDatabase("demo_ocr_learning.pkl")
    
    # Show current stats
    stats = learning_db.get_stats()
    print(f"📊 Current Learning Database:")
    print(f"  - Total corrections: {stats['total_corrections']}")
    print(f"  - High confidence: {stats['high_confidence_corrections']}")
    print(f"  - Average confidence: {stats['average_confidence']:.1f}")
    print()
    
    # Initialize interactive OCR
    interactive_ocr = InteractiveOCR(learning_db)
    
    print("🔍 How the Interactive OCR Works:")
    print()
    print("1. **Character Detection**: OCR extracts each character with confidence score")
    print("2. **Context Analysis**: System analyzes surrounding characters for learning")
    print("3. **User Validation**: You validate or correct each character")
    print("4. **Learning**: System learns from your corrections")
    print("5. **Future Suggestions**: System suggests corrections based on learned patterns")
    print()
    
    # Simulate some learning examples
    print("📚 Simulating Learning Examples:")
    print()
    
    # Add some example corrections
    examples = [
        ("MII?KQI", "1", "I", "In RSA keys, '1' in uppercase context is usually 'I'"),
        ("mDZ?6Nx", "1", "l", "In lowercase context, '1' is usually 'l'"),
        ("Y?4P", "0", "O", "In mixed case, '0' is usually 'O'"),
        ("biB?vn", "G", "6", "In base64, 'G' can be confused with '6'"),
        ("Kd?f57", "L", "l", "Case correction: 'L' should be 'l'"),
    ]
    
    for context, wrong_char, correct_char, explanation in examples:
        learning_db.add_correction(context, wrong_char, correct_char)
        print(f"✅ Learned: '{wrong_char}' → '{correct_char}' in context '{context}'")
        print(f"   Reason: {explanation}")
    
    # Save the learning
    learning_db.save_database()
    print()
    
    # Show updated stats
    stats = learning_db.get_stats()
    print(f"📈 Updated Learning Database:")
    print(f"  - Total corrections: {stats['total_corrections']}")
    print(f"  - High confidence: {stats['high_confidence_corrections']}")
    print(f"  - Average confidence: {stats['average_confidence']:.1f}")
    print()
    
    # Demonstrate suggestions
    print("💡 Testing Learned Suggestions:")
    test_cases = [
        ("MII?KQI", "1"),
        ("mDZ?6Nx", "1"),
        ("Y?4P", "0"),
        ("biB?vn", "G"),
        ("Kd?f57", "L"),
        ("new?context", "X"),  # Should have no suggestion
    ]
    
    for context, test_char in test_cases:
        suggestion = learning_db.get_suggestion(context, test_char)
        if suggestion:
            print(f"🎯 Context '{context}' + char '{test_char}' → Suggests: '{suggestion}'")
        else:
            print(f"❓ Context '{context}' + char '{test_char}' → No suggestion (new pattern)")
    
    print()
    print("🚀 Ready to Use Interactive OCR!")
    print()
    print("To use the interactive system:")
    print("  python interactive_learning_ocr.py key.png --output validated_key.txt")
    print()
    print("Commands during validation:")
    print("  - ENTER: Accept the character")
    print("  - Type character: Correct the character")
    print("  - 'skip': Skip this character")
    print("  - 'quit': Finish early")
    print("  - 'auto': Auto-accept high-confidence characters")
    print("  - 'preview': See character preview")
    print("  - 'context': See extended context")
    print("  - 'y'/'n': Accept/reject learned suggestions")

def demo_auto_correction():
    """Demonstrate auto-correction using learned patterns"""
    print("\n" + "=" * 50)
    print("🤖 Auto-Correction Demo")
    print("=" * 50)
    
    # Load the learning database
    learning_db = LearningDatabase("demo_ocr_learning.pkl")
    
    # Simulate some OCR text with errors
    ocr_text = "MII1KQIBAAKCAgEApXLc+tmBmDZ16NxuM+IohxYT0ph3G/Pt/plQH3KsowSzMG2E"
    
    print("Original OCR text:")
    print(f"'{ocr_text}'")
    print()
    
    # Apply learned corrections
    from interactive_learning_ocr import apply_learned_corrections
    corrected_text = apply_learned_corrections(ocr_text, learning_db)
    
    print("After applying learned corrections:")
    print(f"'{corrected_text}'")
    print()
    
    # Show what changed
    changes = []
    for i, (orig, corr) in enumerate(zip(ocr_text, corrected_text)):
        if orig != corr:
            changes.append(f"Position {i}: '{orig}' → '{corr}'")
    
    if changes:
        print("Changes made:")
        for change in changes:
            print(f"  {change}")
    else:
        print("No changes made (need more learning data)")

if __name__ == "__main__":
    demo_learning_system()
    demo_auto_correction()
