#!/usr/bin/env python3
"""
Robust OCR Script that handles corrupted or problematic images
Version 2 - Enhanced for corrupted PNG files
"""

import argparse
import pytesseract
import os
import sys
import subprocess
import tempfile
from pathlib import Path
import shutil


def try_repair_png(input_path, output_path):
    """
    Try to repair a corrupted PNG file by padding with zeros or truncating
    """
    try:
        with open(input_path, 'rb') as f:
            data = f.read()
        
        print(f"Original file size: {len(data)} bytes")
        
        # Try to find PNG signature and IEND chunk
        png_signature = b'\x89PNG\r\n\x1a\n'
        iend_chunk = b'IEND\xaeB`\x82'
        
        if not data.startswith(png_signature):
            print("✗ Not a valid PNG file (missing PNG signature)")
            return False
        
        # Find the last IEND chunk
        iend_pos = data.rfind(iend_chunk)
        if iend_pos == -1:
            print("✗ No IEND chunk found - trying to add one")
            # Try adding a proper IEND chunk
            repaired_data = data + b'\x00\x00\x00\x00IEND\xaeB`\x82'
        else:
            print(f"✓ Found IEND chunk at position {iend_pos}")
            # Truncate after IEND chunk
            repaired_data = data[:iend_pos + len(iend_chunk)]
        
        # Write repaired file
        with open(output_path, 'wb') as f:
            f.write(repaired_data)
        
        print(f"✓ Repaired PNG saved to {output_path} ({len(repaired_data)} bytes)")
        return True
        
    except Exception as e:
        print(f"✗ PNG repair failed: {e}")
        return False


def extract_text_with_pil_recovery(image_path):
    """
    Try to extract text using PIL with error recovery for corrupted images
    """
    try:
        from PIL import Image, ImageFile
        
        # Enable loading of truncated images
        ImageFile.LOAD_TRUNCATED_IMAGES = True
        
        print("Attempting PIL recovery with LOAD_TRUNCATED_IMAGES=True")
        
        # Try to open and process the image
        with Image.open(image_path) as img:
            print(f"✓ PIL successfully opened image: {img.size}, mode: {img.mode}")
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
                print("✓ Converted to RGB mode")
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                tmp_path = tmp_file.name
                img.save(tmp_path, 'PNG')
                print(f"✓ Saved processed image to {tmp_path}")
            
            try:
                # Try OCR on the PIL-processed image
                text = pytesseract.image_to_string(tmp_path)
                return text.strip()
            finally:
                try:
                    os.unlink(tmp_path)
                except:
                    pass
                    
    except Exception as e:
        print(f"✗ PIL recovery failed: {e}")
        return None


def convert_image_with_imagemagick(input_path, output_path):
    """
    Convert image using ImageMagick to fix potential corruption issues
    Try multiple approaches for corrupted images
    """
    # Try different ImageMagick commands
    commands = [
        # Modern magick command with error tolerance
        ['magick', input_path, '-define', 'png:ignore-crc', output_path],
        # Try with ping to check what's readable
        ['magick', input_path, '-strip', output_path], 
        # Legacy convert command with error tolerance
        ['convert', input_path, '-define', 'png:ignore-crc', output_path],
        # Try to extract partial image
        ['convert', input_path, '-strip', '-quality', '100', output_path],
        # Basic conversion
        ['magick', input_path, output_path],
        ['convert', input_path, output_path]
    ]
    
    for i, cmd in enumerate(commands):
        try:
            print(f"Trying ImageMagick method {i+1}: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Success with method {i+1}")
                return True
            else:
                print(f"✗ Method {i+1} failed: {result.stderr.strip()}")
        except FileNotFoundError:
            if i == 0:  # Only show this message once
                print("ImageMagick not found. Install with: sudo apt-get install imagemagick")
            continue
        except Exception as e:
            print(f"✗ Method {i+1} exception: {e}")
    
    return False


def extract_text_from_converted_image(image_path):
    """
    Extract text using a converted/cleaned image
    """
    try:
        # Create a temporary file for the converted image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        # Convert the image using ImageMagick
        if convert_image_with_imagemagick(image_path, tmp_path):
            print(f"Successfully converted image to: {tmp_path}")
            
            # Now try OCR on the converted image
            try:
                text = pytesseract.image_to_string(tmp_path)
                return text.strip()
            except Exception as e:
                print(f"OCR failed on converted image: {e}")
                return None
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except:
                    pass
        else:
            return None
            
    except Exception as e:
        print(f"Error in image conversion process: {e}")
        return None


def extract_text_from_repaired_image(image_path):
    """
    Try to repair the PNG file first, then extract text
    """
    try:
        # Create a temporary file for the repaired image
        with tempfile.NamedTemporaryFile(suffix='_repaired.png', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        # Try to repair the PNG
        if try_repair_png(image_path, tmp_path):
            print(f"Successfully repaired PNG to: {tmp_path}")
            
            # Now try OCR on the repaired image
            try:
                text = pytesseract.image_to_string(tmp_path)
                return text.strip()
            except Exception as e:
                print(f"OCR failed on repaired image: {e}")
                return None
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except:
                    pass
        else:
            return None
            
    except Exception as e:
        print(f"Error in image repair process: {e}")
        return None


def extract_text_direct(image_path):
    """
    Try direct OCR without any preprocessing
    """
    try:
        text = pytesseract.image_to_string(image_path)
        return text.strip()
    except Exception as e:
        print(f"Direct OCR failed: {e}")
        return None


def extract_text_with_configs(image_path):
    """
    Try different tesseract configurations
    """
    configs = [
        "--psm 6",
        "--psm 8", 
        "--psm 7",
        "--psm 13",
        "--psm 3",
        "--oem 3 --psm 6",
        "--oem 1 --psm 6"
    ]
    
    results = {}
    
    for config in configs:
        try:
            print(f"Trying config: {config}")
            text = pytesseract.image_to_string(image_path, config=config)
            if text.strip():
                results[config] = text.strip()
                print(f"✓ Found text with config {config}")
            else:
                print(f"✗ No text found with config {config}")
        except Exception as e:
            print(f"✗ Config {config} failed: {e}")
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Robust OCR tool for problematic images")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file to save extracted text")
    parser.add_argument("--convert-only", action="store_true", help="Only convert the image, don't do OCR")
    parser.add_argument("--repair-only", action="store_true", help="Only repair the PNG file")
    
    args = parser.parse_args()
    
    # Check if image file exists
    if not os.path.exists(args.image_path):
        print(f"Error: Image file '{args.image_path}' not found")
        sys.exit(1)
    
    print(f"Processing image: {args.image_path}")
    
    if args.repair_only:
        # Just repair the PNG file
        output_path = args.image_path.replace('.png', '_repaired.png')
        if try_repair_png(args.image_path, output_path):
            print(f"Repaired image saved to: {output_path}")
        return
    
    if args.convert_only:
        # Just convert the image to fix potential issues
        output_path = args.image_path.replace('.png', '_converted.png')
        if convert_image_with_imagemagick(args.image_path, output_path):
            print(f"Converted image saved to: {output_path}")
        return
    
    all_results = []
    
    # Method 1: Try PIL with recovery mode
    print("\n=== Method 1: PIL with LOAD_TRUNCATED_IMAGES ===")
    pil_result = extract_text_with_pil_recovery(args.image_path)
    if pil_result:
        print("✓ PIL recovery OCR succeeded:")
        print(repr(pil_result))
        all_results.append(("PIL Recovery", pil_result))
    else:
        print("✗ PIL recovery OCR failed")
    
    # Method 2: Try PNG repair first
    print("\n=== Method 2: PNG Repair + OCR ===")
    repair_result = extract_text_from_repaired_image(args.image_path)
    if repair_result:
        print("✓ Repaired PNG OCR succeeded:")
        print(repr(repair_result))
        all_results.append(("PNG Repair + OCR", repair_result))
    else:
        print("✗ Repaired PNG OCR failed")
    
    # Method 3: Convert with ImageMagick then OCR
    print("\n=== Method 3: ImageMagick Conversion + OCR ===")
    converted_result = extract_text_from_converted_image(args.image_path)
    if converted_result:
        print("✓ Converted image OCR succeeded:")
        print(repr(converted_result))
        all_results.append(("ImageMagick + OCR", converted_result))
    else:
        print("✗ Converted image OCR failed")
    
    # Method 4: Try direct OCR (will probably fail but let's try)
    print("\n=== Method 4: Direct OCR ===")
    direct_result = extract_text_direct(args.image_path)
    if direct_result:
        print("✓ Direct OCR succeeded:")
        print(repr(direct_result))
        all_results.append(("Direct OCR", direct_result))
    else:
        print("✗ Direct OCR failed")
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY OF RESULTS")
    print("="*50)
    
    if all_results:
        for i, (method, text) in enumerate(all_results, 1):
            print(f"\n{i}. {method}:")
            print("-" * (len(method) + 3))
            print(text[:500] + ("..." if len(text) > 500 else ""))
    else:
        print("No text could be extracted from the image.")
        print("\nTry these manual steps:")
        print("1. python robust_ocr_v2.py key.png --repair-only")
        print("2. python robust_ocr_v2.py key.png --convert-only")
        print("3. View the repaired/converted files manually")
    
    # Save results if requested
    if args.output and all_results:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write("OCR Results Summary\n")
                f.write("="*50 + "\n\n")
                for method, text in all_results:
                    f.write(f"{method}:\n")
                    f.write("-" * (len(method) + 1) + "\n")
                    f.write(text + "\n\n")
            print(f"\nResults saved to: {args.output}")
        except Exception as e:
            print(f"Error saving to file: {e}")


if __name__ == "__main__":
    main()
