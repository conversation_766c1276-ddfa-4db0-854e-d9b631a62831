#!/usr/bin/env python3
"""
Robust OCR Script that handles corrupted or problematic images
"""

import argparse
import pytesseract
import os
import sys
import subprocess
import tempfile
from pathlib import Path
import shutil


def try_repair_png(input_path, output_path):
    """
    Try to repair a corrupted PNG file by padding with zeros or truncating
    """
    try:
        with open(input_path, 'rb') as f:
            data = f.read()
        
        print(f"Original file size: {len(data)} bytes")
        
        # Try to find PNG signature and IEND chunk
        png_signature = b'\x89PNG\r\n\x1a\n'
        iend_chunk = b'IEND\xaeB`\x82'
        
        if not data.startswith(png_signature):
            print("✗ Not a valid PNG file (missing PNG signature)")
            return False
        
        # Find the last IEND chunk
        iend_pos = data.rfind(iend_chunk)
        if iend_pos == -1:
            print("✗ No IEND chunk found - trying to add one")
            # Try adding a proper IEND chunk
            repaired_data = data + b'\x00\x00\x00\x00IEND\xaeB`\x82'
        else:
            print(f"✓ Found IEND chunk at position {iend_pos}")
            # Truncate after IEND chunk
            repaired_data = data[:iend_pos + len(iend_chunk)]
        
        # Write repaired file
        with open(output_path, 'wb') as f:
            f.write(repaired_data)
        
        print(f"✓ Repaired PNG saved to {output_path} ({len(repaired_data)} bytes)")
        return True
        
    except Exception as e:
        print(f"✗ PNG repair failed: {e}")
        return False


def convert_image_with_imagemagick(input_path, output_path):
    """
    Convert image using ImageMagick to fix potential corruption issues
    Try multiple approaches for corrupted images
    """
    # Try different ImageMagick commands
    commands = [
        # Modern magick command
        ['magick', input_path, output_path],
        # Try with error recovery
        ['magick', input_path, '-define', 'png:ignore-crc', output_path],
        # Force read what's available
        ['magick', '-ping', input_path, '-strip', output_path],
        # Legacy convert command with error tolerance
        ['convert', input_path, '-define', 'png:ignore-crc', output_path],
        # Try to extract partial image
        ['convert', input_path, '-strip', '-quality', '100', output_path]
    ]
    
    for i, cmd in enumerate(commands):
        try:
            print(f"Trying ImageMagick method {i+1}: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ Success with method {i+1}")
                return True
            else:
                print(f"✗ Method {i+1} failed: {result.stderr.strip()}")
        except FileNotFoundError:
            if i == 0:  # Only show this message once
                print("ImageMagick not found. Install with: sudo apt-get install imagemagick")
            continue
        except Exception as e:
            print(f"✗ Method {i+1} exception: {e}")
    
    return False


def extract_text_with_pil_recovery(image_path):
    """
    Try to extract text using PIL with error recovery for corrupted images
    """
    try:
        from PIL import Image, ImageFile
        
        # Enable loading of truncated images
        ImageFile.LOAD_TRUNCATED_IMAGES = True
        
        # Try to open and process the image
        with Image.open(image_path) as img:
            print(f"✓ PIL successfully opened image: {img.size}, mode: {img.mode}")
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                tmp_path = tmp_file.name
                img.save(tmp_path, 'PNG')
            
            try:
                # Try OCR on the PIL-processed image
                text = pytesseract.image_to_string(tmp_path)
                return text.strip()
            finally:
                try:
                    os.unlink(tmp_path)
                except:
                    pass
                    
    except Exception as e:
        print(f"✗ PIL recovery failed: {e}")
        return None
    """
    Extract text using a converted/cleaned image
    """
    try:
        # Create a temporary file for the converted image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        # Convert the image using ImageMagick
        if convert_image_with_imagemagick(image_path, tmp_path):
            print(f"Successfully converted image to: {tmp_path}")
            
            # Now try OCR on the converted image
            try:
                text = pytesseract.image_to_string(tmp_path)
                return text.strip()
            except Exception as e:
                print(f"OCR failed on converted image: {e}")
                return None
            finally:
                # Clean up temporary file
                try:
                    os.unlink(tmp_path)
                except:
                    pass
        else:
            return None
            
    except Exception as e:
        print(f"Error in image conversion process: {e}")
        return None


def extract_text_direct(image_path):
    """
    Try direct OCR without any preprocessing
    """
    try:
        text = pytesseract.image_to_string(image_path)
        return text.strip()
    except Exception as e:
        print(f"Direct OCR failed: {e}")
        return None


def extract_text_with_configs(image_path):
    """
    Try different tesseract configurations
    """
    configs = [
        "--psm 6",
        "--psm 8", 
        "--psm 7",
        "--psm 13",
        "--psm 3",
        "--oem 3 --psm 6",
        "--oem 1 --psm 6"
    ]
    
    results = {}
    
    for config in configs:
        try:
            print(f"Trying config: {config}")
            text = pytesseract.image_to_string(image_path, config=config)
            if text.strip():
                results[config] = text.strip()
                print(f"✓ Found text with config {config}")
            else:
                print(f"✗ No text found with config {config}")
        except Exception as e:
            print(f"✗ Config {config} failed: {e}")
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Robust OCR tool for problematic images")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file to save extracted text")
    parser.add_argument("--convert-only", action="store_true", help="Only convert the image, don't do OCR")
    
    args = parser.parse_args()
    
    # Check if image file exists
    if not os.path.exists(args.image_path):
        print(f"Error: Image file '{args.image_path}' not found")
        sys.exit(1)
    
    print(f"Processing image: {args.image_path}")
    
    if args.convert_only:
        # Just convert the image to fix potential issues
        output_path = args.image_path.replace('.png', '_converted.png')
        if convert_image_with_imagemagick(args.image_path, output_path):
            print(f"Converted image saved to: {output_path}")
        return
    
    all_results = []
    
    # Method 1: Try direct OCR
    print("\n=== Method 1: Direct OCR ===")
    direct_result = extract_text_direct(args.image_path)
    if direct_result:
        print("✓ Direct OCR succeeded:")
        print(direct_result)
        all_results.append(("Direct OCR", direct_result))
    else:
        print("✗ Direct OCR failed")
    
    # Method 2: Convert with ImageMagick then OCR
    print("\n=== Method 2: ImageMagick Conversion + OCR ===")
    converted_result = extract_text_from_converted_image(args.image_path)
    if converted_result:
        print("✓ Converted image OCR succeeded:")
        print(converted_result)
        all_results.append(("ImageMagick + OCR", converted_result))
    else:
        print("✗ Converted image OCR failed")
    
    # Method 3: Try different tesseract configs on converted image
    print("\n=== Method 3: Multiple Tesseract Configurations ===")
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
        tmp_path = tmp_file.name
    
    try:
        if convert_image_with_imagemagick(args.image_path, tmp_path):
            config_results = extract_text_with_configs(tmp_path)
            for config, text in config_results.items():
                all_results.append((f"Config: {config}", text))
    finally:
        try:
            os.unlink(tmp_path)
        except:
            pass
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY OF RESULTS")
    print("="*50)
    
    if all_results:
        for i, (method, text) in enumerate(all_results, 1):
            print(f"\n{i}. {method}:")
            print("-" * (len(method) + 3))
            print(text[:500] + ("..." if len(text) > 500 else ""))
    else:
        print("No text could be extracted from the image.")
    
    # Save results if requested
    if args.output and all_results:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write("OCR Results Summary\n")
                f.write("="*50 + "\n\n")
                for method, text in all_results:
                    f.write(f"{method}:\n")
                    f.write("-" * (len(method) + 1) + "\n")
                    f.write(text + "\n\n")
            print(f"\nResults saved to: {args.output}")
        except Exception as e:
            print(f"Error saving to file: {e}")


if __name__ == "__main__":
    main()
