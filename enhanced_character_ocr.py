#!/usr/bin/env python3
"""
Enhanced Character-by-Character Interactive OCR with Navigation
Shows full word/line context with character position highlighting and backward navigation
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging
from difflib import SequenceMatcher

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCharacterOCR:
    """Enhanced character-by-character OCR with navigation and visual context"""
    
    def __init__(self):
        self.validated_chars = []
        self.current_position = 0
        self.text_to_validate = ""
        self.image_path = ""
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def get_word_context(self, text: str, char_index: int) -> tuple:
        """Get the word containing the current character and its position within the word"""
        # Find word boundaries
        words = []
        word_positions = []
        current_word = ""
        word_start = 0
        
        for i, char in enumerate(text):
            if char.isalnum():
                if not current_word:
                    word_start = i
                current_word += char
            else:
                if current_word:
                    words.append(current_word)
                    word_positions.append((word_start, word_start + len(current_word) - 1))
                    current_word = ""
                # Handle non-alphanumeric characters as single-character "words"
                words.append(char)
                word_positions.append((i, i))
        
        # Handle last word if text doesn't end with non-alphanumeric
        if current_word:
            words.append(current_word)
            word_positions.append((word_start, word_start + len(current_word) - 1))
        
        # Find which word contains the current character
        for i, (start, end) in enumerate(word_positions):
            if start <= char_index <= end:
                return words[i], char_index - start, i, start, end
        
        # Fallback
        return text[char_index], 0, 0, char_index, char_index
    
    def create_context_image(self, char_filename: str = "char.png"):
        """Create visual context showing current character position"""
        try:
            char = self.text_to_validate[self.current_position] if self.current_position < len(self.text_to_validate) else '?'
            
            # Get word context
            word, char_in_word, word_index, word_start, word_end = self.get_word_context(
                self.text_to_validate, self.current_position
            )
            
            # Load and preprocess original image for display
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            
            # Apply same preprocessing for consistency
            img_array = np.array(img)
            processed_img = self._preprocess_image(img_array)
            
            # Scale the processed image for better visibility
            scale = 2
            height, width = processed_img.shape
            new_width = width // scale  # Reduce size for display
            new_height = height // scale
            display_img = cv2.resize(processed_img, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # Convert back to PIL
            pil_display = Image.fromarray(display_img).convert('RGB')
            
            # Create final image with info
            info_height = 200
            final_width = max(pil_display.width, 800)
            final_height = pil_display.height + info_height
            
            final_img = Image.new('RGB', (final_width, final_height), 'white')
            
            # Center the processed image
            img_x = (final_width - pil_display.width) // 2
            final_img.paste(pil_display, (img_x, 0))
            
            # Add border around image
            draw = ImageDraw.Draw(final_img)
            draw.rectangle([img_x-2, -2, img_x+pil_display.width+1, pil_display.height+1], 
                         outline='blue', width=2)
            
            # Add info text below
            try:
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            except:
                font_large = None
                font_small = None
            
            info_y = pil_display.height + 20
            
            # Title
            draw.text((20, info_y), "Character-by-Character OCR Validation", fill='darkblue', font=font_large)
            
            # Current character info
            draw.text((20, info_y + 25), f"Current Character: '{char}' (Position {self.current_position + 1} of {len(self.text_to_validate)})", 
                     fill='red', font=font_large)
            
            # Character details
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            draw.text((20, info_y + 50), f"ASCII: {ord(char)} (0x{ord(char):02x}) | Type: {char_type}", 
                     fill='blue', font=font_small)
            
            # Word context
            if len(word) > 1:
                # Show word with current character highlighted
                word_display = ""
                for i, c in enumerate(word):
                    if i == char_in_word:
                        word_display += f"[{c}]"
                    else:
                        word_display += c
                
                draw.text((20, info_y + 75), f"Word Context: '{word_display}' (Character {char_in_word + 1} of {len(word)})", 
                         fill='green', font=font_small)
            else:
                draw.text((20, info_y + 75), f"Single Character: '{char}'", fill='green', font=font_small)
            
            # Progress context
            start = max(0, self.current_position - 10)
            end = min(len(self.text_to_validate), self.current_position + 11)
            context = self.text_to_validate[start:end]
            
            # Build context display with current character highlighted
            context_display = ""
            for i in range(start, end):
                if i < len(self.validated_chars):
                    # Already validated
                    context_display += self.validated_chars[i]
                elif i == self.current_position:
                    # Current character
                    context_display += f"[{self.text_to_validate[i]}]"
                else:
                    # Not yet validated
                    context_display += self.text_to_validate[i]
            
            draw.text((20, info_y + 100), f"Context: {repr(context_display)}", fill='purple', font=font_small)
            
            # Validation progress
            validated_count = len(self.validated_chars)
            draw.text((20, info_y + 125), f"Progress: {validated_count}/{len(self.text_to_validate)} characters validated", 
                     fill='orange', font=font_small)
            
            # Navigation help
            draw.text((20, info_y + 150), "Commands: ENTER=accept, type=correct, 'back'/'b'=previous, 'skip'=skip, 'quit'=exit", 
                     fill='gray', font=font_small)
            
            # Save the image
            final_img.save(char_filename)
            logger.debug(f"Created context image: {char_filename}")
            
        except Exception as e:
            logger.error(f"Failed to create context image: {e}")
            self._create_fallback_image(char_filename)
    
    def _create_fallback_image(self, char_filename: str):
        """Create a simple fallback visualization"""
        try:
            char = self.text_to_validate[self.current_position] if self.current_position < len(self.text_to_validate) else '?'
            img = Image.new('RGB', (600, 300), 'white')
            draw = ImageDraw.Draw(img)
            
            try:
                font = ImageFont.load_default()
            except:
                font = None
            
            draw.text((50, 50), f"Character: '{char}'", fill='red', font=font)
            draw.text((50, 80), f"Position: {self.current_position + 1} of {len(self.text_to_validate)}", fill='blue', font=font)
            draw.text((50, 110), "Fallback mode - image processing failed", fill='gray', font=font)
            
            img.save(char_filename)
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
    
    def navigate_to_position(self, new_position: int):
        """Navigate to a specific character position"""
        if 0 <= new_position < len(self.text_to_validate):
            self.current_position = new_position
            # Adjust validated_chars list to match new position
            if len(self.validated_chars) > new_position:
                self.validated_chars = self.validated_chars[:new_position]
            return True
        return False
    
    def enhanced_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform enhanced character-by-character validation with navigation"""
        print("🔍 Enhanced Character-by-Character OCR Validation")
        print("=" * 70)
        
        self.image_path = image_path
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        self.text_to_validate = '\n'.join(lines)
        self.validated_chars = []
        self.current_position = 0
        
        print(f"📝 Text to validate ({len(self.text_to_validate)} characters):")
        print(f"Preview: {repr(self.text_to_validate[:100])}...")
        print()
        print("💡 Enhanced Commands:")
        print("  - ENTER: Accept the character")
        print("  - Type character: Correct the character")
        print("  - 'back' or 'b': Go back to previous character")
        print("  - 'skip': Skip this character")
        print("  - 'quit': Finish validation")
        print("  - 'auto': Auto-accept remaining alphanumeric characters")
        print("  - Check char.png for visual context with position highlighting")
        print()
        
        corrections_made = 0
        auto_mode = False
        
        while self.current_position < len(self.text_to_validate):
            char = self.text_to_validate[self.current_position]
            
            # Create context visualization
            self.create_context_image("char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                self.validated_chars.append(char)
                self.current_position += 1
                continue
            
            # Display character info
            print(f"Character {self.current_position + 1}/{len(self.text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show word context
            word, char_in_word, word_index, word_start, word_end = self.get_word_context(
                self.text_to_validate, self.current_position
            )
            
            if len(word) > 1:
                word_display = ""
                for i, c in enumerate(word):
                    if i == char_in_word:
                        word_display += f"[{c}]"
                    else:
                        word_display += c
                print(f"Word context: '{word_display}' (character {char_in_word + 1} of {len(word)})")
            
            # Show progress context
            start = max(0, self.current_position - 10)
            end = min(len(self.text_to_validate), self.current_position + 11)
            
            context_display = ""
            for i in range(start, end):
                if i < len(self.validated_chars):
                    context_display += self.validated_chars[i]
                elif i == self.current_position:
                    context_display += f"[{self.text_to_validate[i]}]"
                else:
                    context_display += self.text_to_validate[i]
            
            print(f"Context: {repr(context_display)}")
            print(f"Progress: {len(self.validated_chars)}/{len(self.text_to_validate)} validated")
            print(f"📷 Visual context saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Validate character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    self.validated_chars.append(char)
                    self.current_position += 1
                    break
                elif user_input.lower() in ["back", "b"]:
                    # Go back to previous character
                    if self.current_position > 0:
                        self.current_position -= 1
                        if len(self.validated_chars) > self.current_position:
                            self.validated_chars = self.validated_chars[:self.current_position]
                        print(f"⬅️  Moved back to position {self.current_position + 1}")
                        break
                    else:
                        print("❌ Already at the beginning")
                elif user_input.lower() == "skip":
                    # Skip this character
                    self.current_position += 1
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(self.validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    self.validated_chars.append(char)
                    self.current_position += 1
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    self.validated_chars.append(user_input)
                    self.current_position += 1
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    self.validated_chars.append('\n')
                    self.current_position += 1
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter: single character, 'back'/'b', 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(self.validated_chars)
        print("=" * 70)
        print("✅ Enhanced Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(self.text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="Enhanced Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize enhanced character-by-character OCR
    enhanced_ocr = EnhancedCharacterOCR()
    
    # Perform enhanced character-by-character validation
    validated_text = enhanced_ocr.enhanced_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
