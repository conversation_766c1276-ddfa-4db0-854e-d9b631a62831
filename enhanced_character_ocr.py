#!/usr/bin/env python3
"""
Enhanced Character-by-Character Interactive OCR with Navigation
Shows full word/line context with character position highlighting and backward navigation
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging
from difflib import SequenceMatcher

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedCharacterOCR:
    """Enhanced character-by-character OCR with navigation and visual context"""
    
    def __init__(self):
        self.validated_chars = []
        self.current_position = 0
        self.text_to_validate = ""
        self.image_path = ""
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def get_word_context(self, text: str, char_index: int) -> tuple:
        """Get the word containing the current character and its position within the word"""
        # Find word boundaries
        words = []
        word_positions = []
        current_word = ""
        word_start = 0
        
        for i, char in enumerate(text):
            if char.isalnum():
                if not current_word:
                    word_start = i
                current_word += char
            else:
                if current_word:
                    words.append(current_word)
                    word_positions.append((word_start, word_start + len(current_word) - 1))
                    current_word = ""
                # Handle non-alphanumeric characters as single-character "words"
                words.append(char)
                word_positions.append((i, i))
        
        # Handle last word if text doesn't end with non-alphanumeric
        if current_word:
            words.append(current_word)
            word_positions.append((word_start, word_start + len(current_word) - 1))
        
        # Find which word contains the current character
        for i, (start, end) in enumerate(word_positions):
            if start <= char_index <= end:
                return words[i], char_index - start, i, start, end
        
        # Fallback
        return text[char_index], 0, 0, char_index, char_index
    
    def extract_word_region_with_ocr(self, word_start: int, word_end: int) -> Image.Image:
        """Extract word region using OCR character-level detection"""
        try:
            # Load and preprocess image
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Apply preprocessing for better OCR
            img_array = np.array(img)
            processed_img = self._preprocess_image(img_array)
            pil_img = Image.fromarray(processed_img)

            # Get character-level OCR data with higher precision
            data = pytesseract.image_to_data(pil_img, config="--psm 8 --oem 1 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-/=", output_type=pytesseract.Output.DICT)

            # Get the target word from our text
            target_word = self.text_to_validate[word_start:word_end+1].strip()
            print(f"🎯 Looking for word: '{target_word}' in OCR results")

            # Try to find character-level matches
            char_boxes = []
            for i in range(len(data['text'])):
                ocr_char = data['text'][i].strip()
                confidence = data['conf'][i]

                if ocr_char and confidence > 20:  # Lower threshold for characters
                    char_boxes.append({
                        'char': ocr_char,
                        'confidence': confidence,
                        'left': data['left'][i],
                        'top': data['top'][i],
                        'width': data['width'][i],
                        'height': data['height'][i]
                    })

            print(f"📊 Found {len(char_boxes)} character boxes with confidence > 20")

            # If we have character boxes, try to find the target word region
            if char_boxes and len(target_word) <= 10:  # For short words/sequences
                # Find the best matching sequence
                best_region = self._find_character_sequence(char_boxes, target_word, img)
                if best_region:
                    print(f"✅ Found character sequence match")
                    return best_region

            # Fallback: try word-level detection with relaxed settings
            word_data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)

            best_bbox = None
            best_score = 0

            for i in range(len(word_data['text'])):
                ocr_word = word_data['text'][i].strip()
                confidence = word_data['conf'][i]

                if ocr_word and confidence > 10:  # Very low threshold
                    # Check similarity
                    similarity = self._calculate_word_similarity(ocr_word, target_word)
                    score = similarity * (confidence / 100.0)

                    print(f"🔍 OCR word: '{ocr_word}' vs target: '{target_word}' | similarity: {similarity:.2f} | confidence: {confidence} | score: {score:.2f}")

                    if score > best_score:
                        best_score = score
                        best_bbox = {
                            'left': word_data['left'][i],
                            'top': word_data['top'][i],
                            'width': word_data['width'][i],
                            'height': word_data['height'][i]
                        }

            if best_bbox and best_score > 0.1:  # Very low threshold
                print(f"✅ Using OCR word match with score: {best_score:.2f}")
                # Extract word region with minimal padding
                padding = 3
                left = max(0, best_bbox['left'] - padding)
                top = max(0, best_bbox['top'] - padding)
                right = min(img.width, best_bbox['left'] + best_bbox['width'] + padding)
                bottom = min(img.height, best_bbox['top'] + best_bbox['height'] + padding)

                # Ensure minimum size for visibility
                min_width = 40
                min_height = 20

                if right - left < min_width:
                    center_x = (left + right) // 2
                    left = max(0, center_x - min_width // 2)
                    right = min(img.width, center_x + min_width // 2)

                if bottom - top < min_height:
                    center_y = (top + bottom) // 2
                    top = max(0, center_y - min_height // 2)
                    bottom = min(img.height, center_y + min_height // 2)

                return img.crop((left, top, right, bottom))
            else:
                print(f"❌ No good OCR match found, using estimation fallback")
                # Fallback to estimation
                return self._estimate_word_region_precise(word_start, word_end)

        except Exception as e:
            logger.error(f"Failed to extract word region with OCR: {e}")
            print(f"❌ OCR extraction failed: {e}")
            return self._estimate_word_region_precise(word_start, word_end)

    def _find_character_sequence(self, char_boxes: list, target_word: str, img: Image.Image) -> Image.Image:
        """Try to find a sequence of characters matching the target word"""
        try:
            if len(target_word) == 1:
                # For single characters, find the best match
                best_box = None
                best_score = 0

                for box in char_boxes:
                    if box['char'].lower() == target_word.lower():
                        score = box['confidence'] / 100.0
                        if score > best_score:
                            best_score = score
                            best_box = box

                if best_box:
                    padding = 2
                    left = max(0, best_box['left'] - padding)
                    top = max(0, best_box['top'] - padding)
                    right = min(img.width, best_box['left'] + best_box['width'] + padding)
                    bottom = min(img.height, best_box['top'] + best_box['height'] + padding)

                    return img.crop((left, top, right, bottom))

            return None

        except Exception as e:
            logger.error(f"Failed to find character sequence: {e}")
            return None

    def _calculate_word_similarity(self, word1: str, word2: str) -> float:
        """Calculate similarity between two words"""
        if not word1 or not word2:
            return 0.0

        # Simple character-based similarity
        word1_lower = word1.lower()
        word2_lower = word2.lower()

        # Check for substring matches
        if word1_lower in word2_lower or word2_lower in word1_lower:
            return 0.8

        # Character overlap similarity
        common_chars = len(set(word1_lower) & set(word2_lower))
        total_chars = len(set(word1_lower) | set(word2_lower))

        return common_chars / total_chars if total_chars > 0 else 0.0

    def extract_character_region_precise(self, char_position: int) -> Image.Image:
        """Extract a precise character region using layout analysis"""
        try:
            # Load original image
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Get text layout information
            lines = self.text_to_validate.split('\n')

            # Find which line contains this character
            current_pos = 0
            target_line = 0
            char_in_line = char_position

            for line_idx, line in enumerate(lines):
                if current_pos + len(line) >= char_position:
                    target_line = line_idx
                    char_in_line = char_position - current_pos
                    break
                current_pos += len(line) + 1  # +1 for newline

            print(f"📍 Character {char_position} is in line {target_line}, position {char_in_line}")

            # Estimate character dimensions based on image and text
            img_height = img.height
            img_width = img.width

            # Estimate line height (more realistic for text images)
            # For typical text images, line height is much smaller than total height
            if len(lines) == 1:
                # Single line - use a reasonable fraction of image height
                line_height = min(img_height, 50)  # Cap at 50px for single line
            else:
                # Multiple lines - divide height by number of lines
                line_height = img_height // len(lines)

            # Estimate character width (monospace assumption)
            line_text = lines[target_line] if target_line < len(lines) else ""
            if line_text:
                char_width = img_width // len(line_text)
            else:
                char_width = 12  # fallback

            # Calculate character position
            char_y = target_line * line_height
            char_x = char_in_line * char_width

            # Define extraction region with padding
            padding = 3
            char_region_width = max(char_width + 2 * padding, 20)  # minimum 20px width
            char_region_height = max(line_height + 2 * padding, 15)  # minimum 15px height

            # Calculate bounds
            left = max(0, char_x - padding)
            top = max(0, char_y - padding)
            right = min(img.width, left + char_region_width)
            bottom = min(img.height, top + char_region_height)

            # Ensure minimum size
            if right - left < 20:
                right = min(img.width, left + 20)
            if bottom - top < 15:
                bottom = min(img.height, top + 15)

            print(f"📏 Character region: ({left}, {top}) to ({right}, {bottom}) = {right-left}x{bottom-top}")

            # Extract the character region
            char_region = img.crop((left, top, right, bottom))
            return char_region

        except Exception as e:
            logger.error(f"Failed to extract character region: {e}")
            print(f"❌ Character extraction failed: {e}")
            # Fallback to a small region from center
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            center_x, center_y = img.width // 2, img.height // 2
            return img.crop((center_x - 25, center_y - 15, center_x + 25, center_y + 15))

    def extract_word_region(self, word_start: int, word_end: int) -> Image.Image:
        """Extract the word region from the original image"""
        # For single characters, use precise character extraction
        if word_start == word_end:
            return self.extract_character_region_precise(word_start)

        # For multi-character words, extract the range
        try:
            # Load original image
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Get text layout information
            lines = self.text_to_validate.split('\n')

            # Find which line contains the word
            current_pos = 0
            target_line = 0
            word_start_in_line = word_start
            word_end_in_line = word_end

            for line_idx, line in enumerate(lines):
                if current_pos + len(line) >= word_start:
                    target_line = line_idx
                    word_start_in_line = word_start - current_pos
                    word_end_in_line = word_end - current_pos
                    break
                current_pos += len(line) + 1  # +1 for newline

            print(f"📍 Word chars {word_start}-{word_end} in line {target_line}, positions {word_start_in_line}-{word_end_in_line}")

            # Estimate dimensions
            img_height = img.height
            img_width = img.width

            # Estimate line height (more realistic for text images)
            if len(lines) == 1:
                line_height = min(img_height, 50)  # Cap at 50px for single line
            else:
                line_height = img_height // len(lines)

            line_text = lines[target_line] if target_line < len(lines) else ""
            if line_text:
                char_width = img_width // len(line_text)
            else:
                char_width = 12

            # Calculate word region
            word_y = target_line * line_height
            word_x_start = word_start_in_line * char_width
            word_x_end = (word_end_in_line + 1) * char_width

            # Define extraction region with padding
            padding = 5
            left = max(0, word_x_start - padding)
            top = max(0, word_y - padding)
            right = min(img.width, word_x_end + padding)
            bottom = min(img.height, word_y + line_height + padding)

            # Ensure minimum size
            min_width = 30
            min_height = 20
            if right - left < min_width:
                center_x = (left + right) // 2
                left = max(0, center_x - min_width // 2)
                right = min(img.width, center_x + min_width // 2)
            if bottom - top < min_height:
                center_y = (top + bottom) // 2
                top = max(0, center_y - min_height // 2)
                bottom = min(img.height, center_y + min_height // 2)

            print(f"📏 Word region: ({left}, {top}) to ({right}, {bottom}) = {right-left}x{bottom-top}")

            return img.crop((left, top, right, bottom))

        except Exception as e:
            logger.error(f"Failed to extract word region: {e}")
            print(f"❌ Word extraction failed: {e}")
            return self.extract_character_region_precise(word_start)

    def _estimate_word_region_precise(self, word_start: int, word_end: int) -> Image.Image:
        """Precisely estimate word region using character-level positioning"""
        try:
            img = Image.open(self.image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Split text into lines to find which line contains our word
            lines = self.text_to_validate.split('\n')

            # Find which line contains the word
            current_pos = 0
            line_num = 0
            char_in_line_start = word_start

            for i, line in enumerate(lines):
                line_end = current_pos + len(line)
                if current_pos <= word_start <= line_end:
                    line_num = i
                    char_in_line_start = word_start - current_pos
                    break
                current_pos += len(line) + 1  # +1 for newline

            # Get the current line
            current_line = lines[line_num] if line_num < len(lines) else ""
            word_length = word_end - word_start + 1

            # Calculate more precise dimensions
            # Assume monospace font with reasonable character spacing
            avg_char_width = img.width / max(len(current_line), 1) if current_line else 20
            line_height = img.height / max(len(lines), 1)

            # Calculate word boundaries with better precision
            word_left = char_in_line_start * avg_char_width
            word_right = (char_in_line_start + word_length) * avg_char_width
            word_top = line_num * line_height
            word_bottom = (line_num + 1) * line_height

            # Add reasonable padding but keep it focused on the word
            padding_x = max(5, int(avg_char_width * 0.5))  # Half character width padding
            padding_y = max(3, int(line_height * 0.1))     # 10% line height padding

            left = max(0, int(word_left - padding_x))
            top = max(0, int(word_top - padding_y))
            right = min(img.width, int(word_right + padding_x))
            bottom = min(img.height, int(word_bottom + padding_y))

            # Ensure minimum size for visibility
            min_width = max(50, int(avg_char_width * 2))
            min_height = max(20, int(line_height * 0.5))

            if right - left < min_width:
                center_x = (left + right) // 2
                left = max(0, center_x - min_width // 2)
                right = min(img.width, center_x + min_width // 2)

            if bottom - top < min_height:
                center_y = (top + bottom) // 2
                top = max(0, center_y - min_height // 2)
                bottom = min(img.height, center_y + min_height // 2)

            return img.crop((left, top, right, bottom))

        except Exception as e:
            logger.error(f"Failed to estimate precise word region: {e}")
            # Return a small region from top-left as last resort
            img = Image.open(self.image_path)
            return img.crop((0, 0, min(100, img.width), min(30, img.height)))

    def _estimate_word_region(self, word_start: int, word_end: int) -> Image.Image:
        """Legacy method - redirects to precise estimation"""
        return self._estimate_word_region_precise(word_start, word_end)

    def create_context_image(self, char_filename: str = "char.png"):
        """Create visual context showing the word containing current character"""
        try:
            char = self.text_to_validate[self.current_position] if self.current_position < len(self.text_to_validate) else '?'

            # Get word context
            word, char_in_word, word_index, word_start, word_end = self.get_word_context(
                self.text_to_validate, self.current_position
            )

            # Extract the word region from the image using OCR-based detection
            target_word = word
            print(f"🔍 Extracting word region for: '{target_word}' (chars {word_start}-{word_end})")
            word_img = self.extract_word_region(word_start, word_end)
            print(f"📏 Extracted region size: {word_img.width}x{word_img.height}")

            # Scale up the word image for better visibility
            scale = 8
            new_width = word_img.width * scale
            new_height = word_img.height * scale
            scaled_word = word_img.resize((new_width, new_height), Image.NEAREST)

            # Create final image with info
            info_height = 180
            final_width = max(new_width + 40, 600)
            final_height = new_height + info_height + 40

            final_img = Image.new('RGB', (final_width, final_height), 'white')

            # Center the word image
            word_x = (final_width - new_width) // 2
            word_y = 20
            final_img.paste(scaled_word, (word_x, word_y))

            # Add border around word
            draw = ImageDraw.Draw(final_img)
            draw.rectangle([word_x-3, word_y-3, word_x+new_width+2, word_y+new_height+2],
                         outline='red', width=4)

            # Add info text below
            try:
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            except:
                font_large = None
                font_small = None

            info_y = word_y + new_height + 20

            # Title
            draw.text((20, info_y), "Word Context - Character Validation", fill='darkblue', font=font_large)

            # Current character info with word highlighting
            word_display = ""
            for i, c in enumerate(word):
                if i == char_in_word:
                    word_display += f"[{c}]"
                else:
                    word_display += c

            draw.text((20, info_y + 25), f"Word: '{word_display}' (Character {char_in_word + 1} of {len(word)})",
                     fill='red', font=font_large)

            # Character details
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            draw.text((20, info_y + 50), f"Current: '{char}' | ASCII: {ord(char)} | Type: {char_type}",
                     fill='blue', font=font_small)

            # Position info
            draw.text((20, info_y + 75), f"Position: {self.current_position + 1} of {len(self.text_to_validate)} total characters",
                     fill='green', font=font_small)

            # Progress info
            validated_count = len(self.validated_chars)
            draw.text((20, info_y + 100), f"Progress: {validated_count}/{len(self.text_to_validate)} characters validated",
                     fill='orange', font=font_small)

            # Word scaling info
            draw.text((20, info_y + 125), f"Word image scaled {scale}x for visibility",
                     fill='purple', font=font_small)

            # Navigation help
            draw.text((20, info_y + 150), "Commands: ENTER=accept, type=correct, 'back'/'b'=previous, 'skip'=skip, 'quit'=exit",
                     fill='gray', font=font_small)

            # Save the image
            final_img.save(char_filename)
            logger.debug(f"Created word context image: {char_filename}")

        except Exception as e:
            logger.error(f"Failed to create context image: {e}")
            self._create_fallback_image(char_filename)
    
    def _create_fallback_image(self, char_filename: str):
        """Create a simple fallback visualization"""
        try:
            char = self.text_to_validate[self.current_position] if self.current_position < len(self.text_to_validate) else '?'
            img = Image.new('RGB', (600, 300), 'white')
            draw = ImageDraw.Draw(img)
            
            try:
                font = ImageFont.load_default()
            except:
                font = None
            
            draw.text((50, 50), f"Character: '{char}'", fill='red', font=font)
            draw.text((50, 80), f"Position: {self.current_position + 1} of {len(self.text_to_validate)}", fill='blue', font=font)
            draw.text((50, 110), "Fallback mode - image processing failed", fill='gray', font=font)
            
            img.save(char_filename)
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
    
    def navigate_to_position(self, new_position: int):
        """Navigate to a specific character position"""
        if 0 <= new_position < len(self.text_to_validate):
            self.current_position = new_position
            # Adjust validated_chars list to match new position
            if len(self.validated_chars) > new_position:
                self.validated_chars = self.validated_chars[:new_position]
            return True
        return False
    
    def enhanced_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform enhanced character-by-character validation with navigation"""
        print("🔍 Enhanced Character-by-Character OCR Validation")
        print("=" * 70)
        
        self.image_path = image_path
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        self.text_to_validate = '\n'.join(lines)
        self.validated_chars = []
        self.current_position = 0
        
        print(f"📝 Text to validate ({len(self.text_to_validate)} characters):")
        print(f"Preview: {repr(self.text_to_validate[:100])}...")
        print()
        print("💡 Enhanced Commands:")
        print("  - ENTER: Accept the character")
        print("  - Type character: Correct the character")
        print("  - 'back' or 'b': Go back to previous character")
        print("  - 'skip': Skip this character")
        print("  - 'quit': Finish validation")
        print("  - 'auto': Auto-accept remaining alphanumeric characters")
        print("  - Check char.png for visual context with position highlighting")
        print()
        
        corrections_made = 0
        auto_mode = False
        
        while self.current_position < len(self.text_to_validate):
            char = self.text_to_validate[self.current_position]
            
            # Create context visualization
            self.create_context_image("char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                self.validated_chars.append(char)
                self.current_position += 1
                continue
            
            # Display character info
            print(f"Character {self.current_position + 1}/{len(self.text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show word context
            word, char_in_word, word_index, word_start, word_end = self.get_word_context(
                self.text_to_validate, self.current_position
            )
            
            if len(word) > 1:
                word_display = ""
                for i, c in enumerate(word):
                    if i == char_in_word:
                        word_display += f"[{c}]"
                    else:
                        word_display += c
                print(f"Word context: '{word_display}' (character {char_in_word + 1} of {len(word)})")
            
            # Show progress context
            start = max(0, self.current_position - 10)
            end = min(len(self.text_to_validate), self.current_position + 11)
            
            context_display = ""
            for i in range(start, end):
                if i < len(self.validated_chars):
                    context_display += self.validated_chars[i]
                elif i == self.current_position:
                    context_display += f"[{self.text_to_validate[i]}]"
                else:
                    context_display += self.text_to_validate[i]
            
            print(f"Context: {repr(context_display)}")
            print(f"Progress: {len(self.validated_chars)}/{len(self.text_to_validate)} validated")
            print(f"📷 Visual context saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Validate character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    self.validated_chars.append(char)
                    self.current_position += 1
                    break
                elif user_input.lower() in ["back", "b"]:
                    # Go back to previous character
                    if self.current_position > 0:
                        self.current_position -= 1
                        if len(self.validated_chars) > self.current_position:
                            self.validated_chars = self.validated_chars[:self.current_position]
                        print(f"⬅️  Moved back to position {self.current_position + 1}")
                        break
                    else:
                        print("❌ Already at the beginning")
                elif user_input.lower() == "skip":
                    # Skip this character
                    self.current_position += 1
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(self.validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    self.validated_chars.append(char)
                    self.current_position += 1
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    self.validated_chars.append(user_input)
                    self.current_position += 1
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    self.validated_chars.append('\n')
                    self.current_position += 1
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter: single character, 'back'/'b', 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(self.validated_chars)
        print("=" * 70)
        print("✅ Enhanced Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(self.text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="Enhanced Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize enhanced character-by-character OCR
    enhanced_ocr = EnhancedCharacterOCR()
    
    # Perform enhanced character-by-character validation
    validated_text = enhanced_ocr.enhanced_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
