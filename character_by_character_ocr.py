#!/usr/bin/env python3
"""
Character-by-Character Interactive OCR with Visual Feedback
Saves each character as char.png for real-time visual validation
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CharacterLearningDatabase:
    """Database to store and learn from character corrections"""
    
    def __init__(self, db_file: str = "char_ocr_learning.pkl"):
        self.db_file = db_file
        self.corrections = {}  # {(char_context, ocr_char): correct_char}
        self.confidence_scores = {}  # Track confidence of corrections
        self.load_database()
    
    def load_database(self):
        """Load existing learning database"""
        if os.path.exists(self.db_file):
            try:
                with open(self.db_file, 'rb') as f:
                    data = pickle.load(f)
                    self.corrections = data.get('corrections', {})
                    self.confidence_scores = data.get('confidence_scores', {})
                logger.info(f"Loaded {len(self.corrections)} learned corrections from database")
            except Exception as e:
                logger.warning(f"Could not load database: {e}")
    
    def save_database(self):
        """Save learning database"""
        try:
            data = {
                'corrections': self.corrections,
                'confidence_scores': self.confidence_scores,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.db_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"Saved {len(self.corrections)} corrections to database")
        except Exception as e:
            logger.error(f"Could not save database: {e}")
    
    def add_correction(self, context: str, ocr_char: str, correct_char: str):
        """Add a character correction to the learning database"""
        key = (context, ocr_char)
        
        if key in self.corrections:
            if self.corrections[key] == correct_char:
                # Increase confidence
                self.confidence_scores[key] = self.confidence_scores.get(key, 1) + 1
            else:
                # Conflicting correction, reset confidence
                self.confidence_scores[key] = 1
        else:
            self.confidence_scores[key] = 1
        
        self.corrections[key] = correct_char
        logger.debug(f"Learned: '{ocr_char}' -> '{correct_char}' in context '{context}'")
    
    def get_suggestion(self, context: str, ocr_char: str) -> Optional[str]:
        """Get a suggested correction based on learned patterns"""
        key = (context, ocr_char)
        if key in self.corrections and self.confidence_scores.get(key, 0) >= 2:
            return self.corrections[key]
        return None

class CharacterByCharacterOCR:
    """Character-by-character OCR with visual feedback"""
    
    def __init__(self, learning_db: CharacterLearningDatabase):
        self.learning_db = learning_db
        self.context_window = 3
    
    def extract_characters_with_positions(self, image_path: str) -> List[Dict]:
        """Extract characters with their positions and confidence scores"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get detailed OCR data with character positions
        data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)
        
        characters = []
        for i in range(len(data['text'])):
            char = data['text'][i]
            conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0
            
            if char.strip():  # Only process non-empty characters
                bbox = {
                    'left': data['left'][i],
                    'top': data['top'][i], 
                    'width': data['width'][i],
                    'height': data['height'][i]
                }
                
                characters.append({
                    'char': char,
                    'confidence': conf,
                    'bbox': bbox,
                    'line_num': data['line_num'][i],
                    'word_num': data['word_num'][i]
                })
        
        return characters
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def save_character_image(self, image_path: str, bbox: Dict, char_filename: str = "char.png", scale: int = 10):
        """Save the current character as char.png for visual feedback"""
        try:
            # Load original image
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')
            
            # Extract character region with padding
            padding = 5
            left = max(0, bbox['left'] - padding)
            top = max(0, bbox['top'] - padding)
            right = min(img.width, bbox['left'] + bbox['width'] + padding)
            bottom = min(img.height, bbox['top'] + bbox['height'] + padding)
            
            char_region = img.crop((left, top, right, bottom))
            
            # Scale up for better visibility
            new_width = char_region.width * scale
            new_height = char_region.height * scale
            char_region = char_region.resize((new_width, new_height), Image.NEAREST)
            
            # Add border and info
            border_size = 20
            info_height = 60
            final_width = new_width + 2 * border_size
            final_height = new_height + 2 * border_size + info_height
            
            # Create final image with white background
            final_img = Image.new('RGB', (final_width, final_height), 'white')
            
            # Paste character region
            final_img.paste(char_region, (border_size, border_size))
            
            # Add border
            draw = ImageDraw.Draw(final_img)
            draw.rectangle([border_size-2, border_size-2, border_size+new_width+1, border_size+new_height+1], 
                         outline='red', width=2)
            
            # Add info text
            try:
                # Try to use a default font
                font = ImageFont.load_default()
            except:
                font = None
            
            info_y = border_size + new_height + 10
            draw.text((10, info_y), f"Character Region (scaled {scale}x)", fill='black', font=font)
            draw.text((10, info_y + 20), f"Original size: {bbox['width']}x{bbox['height']}", fill='black', font=font)
            draw.text((10, info_y + 40), f"Position: ({bbox['left']}, {bbox['top']})", fill='black', font=font)
            
            # Save the character image
            final_img.save(char_filename)
            logger.debug(f"Saved character image to {char_filename}")
            
        except Exception as e:
            logger.error(f"Failed to save character image: {e}")
    
    def get_character_context(self, characters: List[str], index: int, window: int = None) -> str:
        """Get context around a character for learning"""
        if window is None:
            window = self.context_window
            
        start = max(0, index - window)
        end = min(len(characters), index + window + 1)
        context_chars = characters[start:end]
        
        # Replace the target character with a placeholder
        target_pos = index - start
        if 0 <= target_pos < len(context_chars):
            context_chars[target_pos] = '?'
        
        return ''.join(context_chars)
    
    def character_by_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform character-by-character validation with visual feedback"""
        print("🔍 Starting Character-by-Character OCR Validation")
        print("=" * 60)
        
        # Extract characters with positions
        char_data = self.extract_characters_with_positions(image_path)
        
        if not char_data:
            print("❌ No characters detected in image!")
            return ""
        
        # Filter to first N lines only
        if max_lines > 0:
            max_line = min(max_lines, max([c['line_num'] for c in char_data]))
            char_data = [c for c in char_data if c['line_num'] <= max_line]
        
        print(f"📝 Detected {len(char_data)} characters in first {max_lines} lines")
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining high-confidence characters")
        print("  - Check char.png for visual feedback of current character")
        print()
        
        validated_chars = []
        corrections_made = 0
        auto_mode = False
        
        for i, char_info in enumerate(char_data):
            char = char_info['char']
            confidence = char_info['confidence']
            bbox = char_info['bbox']
            
            # Save current character image for visual feedback
            self.save_character_image(image_path, bbox, "char.png")
            
            # Get context for learning
            current_chars = [c['char'] for c in char_data[:i]]
            context = self.get_character_context(current_chars + [char], len(current_chars))
            
            # Check if we have a learned correction
            suggestion = self.learning_db.get_suggestion(context, char)
            
            # Auto-accept high confidence characters in auto mode
            if auto_mode and confidence > 85 and not suggestion:
                validated_chars.append(char)
                continue
            
            # Display character info
            print(f"Character {i+1}/{len(char_data)} (Line {char_info['line_num']})")
            print(f"OCR detected: '{char}' (confidence: {confidence}%)")
            if suggestion:
                print(f"💡 Learned suggestion: '{suggestion}' (type 'y' to use)")
            print(f"Context: '{context}'")
            print(f"Position: ({bbox['left']}, {bbox['top']}) Size: {bbox['width']}x{bbox['height']}")
            
            # Show current progress
            current_progress = ''.join(validated_chars) + f"[{char}]"
            remaining_preview = ''.join([c['char'] for c in char_data[i+1:i+11]])
            print(f"Progress: ...{current_progress[-20:]}...{remaining_preview[:10]}...")
            print(f"📷 Current character saved as: char.png")
            
            # Get user input
            while True:
                if suggestion:
                    user_input = input(f"Correct character ['{char}'] (suggestion: '{suggestion}'): ").strip()
                else:
                    user_input = input(f"Correct character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    validated_chars.append(char)
                    break
                elif user_input.lower() == "y" and suggestion:
                    # Accept the suggestion
                    validated_chars.append(suggestion)
                    corrections_made += 1
                    print(f"✅ Used learned suggestion: '{suggestion}'")
                    break
                elif user_input.lower() == "skip":
                    # Skip this character
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_chars.append(char)
                    print("🤖 Auto mode enabled for high-confidence characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_chars.append(user_input)
                    corrections_made += 1
                    
                    # Learn from the correction
                    self.learning_db.add_correction(context, char, user_input)
                    print(f"✅ Learned: '{char}' -> '{user_input}' in context '{context}'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', or 'y' for suggestions")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_chars)
        print("=" * 60)
        print("✅ Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(char_data)}")
        print(f"🔧 Corrections made: {corrections_made}")
        print(f"📚 Learning database now has {len(self.learning_db.corrections)} corrections")
        
        # Save the learning database
        self.learning_db.save_database()
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    from difflib import SequenceMatcher
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    parser.add_argument("--db-file", "-d", default="char_ocr_learning.pkl", 
                       help="Learning database file")
    parser.add_argument("--stats", "-s", action="store_true",
                       help="Show learning database statistics")
    
    args = parser.parse_args()
    
    # Initialize learning database
    learning_db = CharacterLearningDatabase(args.db_file)
    
    # Show stats if requested
    if args.stats:
        stats = {
            'total_corrections': len(learning_db.corrections),
            'high_confidence_corrections': sum(1 for score in learning_db.confidence_scores.values() if score >= 3),
            'average_confidence': sum(learning_db.confidence_scores.values()) / len(learning_db.confidence_scores) if learning_db.confidence_scores else 0
        }
        print("📊 Learning Database Statistics:")
        print(f"  - Total corrections: {stats['total_corrections']}")
        print(f"  - High confidence: {stats['high_confidence_corrections']}")
        print(f"  - Average confidence: {stats['average_confidence']:.1f}")
        return 0
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize character-by-character OCR
    char_ocr = CharacterByCharacterOCR(learning_db)
    
    # Perform character-by-character validation
    validated_text = char_ocr.character_by_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
