#!/usr/bin/env python3
"""
True Character-by-Character Interactive OCR with Visual Feedback
Forces individual character detection and saves each as char.png
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrueCharacterOCR:
    """True character-by-character OCR with visual feedback"""
    
    def __init__(self):
        self.context_window = 3
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def create_character_image(self, text: str, char_index: int, char_filename: str = "char.png"):
        """Create a visual representation of the current character"""
        try:
            if char_index >= len(text):
                return
            
            char = text[char_index]
            
            # Create a simple text image showing the character and context
            img_width = 400
            img_height = 300
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            try:
                # Try to use a larger font
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            except:
                font_large = None
                font_small = None
            
            # Draw the current character (large)
            char_text = f"Character: '{char}'"
            draw.text((50, 50), char_text, fill='red', font=font_large)
            
            # Draw context
            start = max(0, char_index - 10)
            end = min(len(text), char_index + 11)
            context = text[start:end]
            
            # Highlight current character in context
            before = context[:char_index - start]
            current = context[char_index - start] if char_index - start < len(context) else ''
            after = context[char_index - start + 1:] if char_index - start + 1 < len(context) else ''
            
            context_text = f"Context: {before}[{current}]{after}"
            draw.text((50, 100), context_text, fill='black', font=font_small)
            
            # Draw position info
            pos_text = f"Position: {char_index + 1} of {len(text)}"
            draw.text((50, 130), pos_text, fill='blue', font=font_small)
            
            # Draw ASCII value
            ascii_text = f"ASCII: {ord(char)} (0x{ord(char):02x})"
            draw.text((50, 160), ascii_text, fill='green', font=font_small)
            
            # Draw character type
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol"
            type_text = f"Type: {char_type}"
            draw.text((50, 190), type_text, fill='purple', font=font_small)
            
            # Add border
            draw.rectangle([10, 10, img_width-10, img_height-10], outline='black', width=2)
            
            # Save the image
            img.save(char_filename)
            logger.debug(f"Created character visualization: {char_filename}")
            
        except Exception as e:
            logger.error(f"Failed to create character image: {e}")
    
    def character_by_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform true character-by-character validation"""
        print("🔍 Starting True Character-by-Character OCR Validation")
        print("=" * 60)
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        text_to_validate = '\n'.join(lines)
        
        print(f"📝 Text to validate ({len(text_to_validate)} characters):")
        print(f"Preview: {repr(text_to_validate[:100])}...")
        print()
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining characters")
        print("  - Check char.png for visual feedback of current character")
        print()
        
        validated_chars = []
        corrections_made = 0
        auto_mode = False
        
        for i, char in enumerate(text_to_validate):
            # Create character visualization
            self.create_character_image(text_to_validate, i, "char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                validated_chars.append(char)
                continue
            
            # Display character info
            print(f"Character {i+1}/{len(text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show context
            start = max(0, i - 10)
            end = min(len(text_to_validate), i + 11)
            context = text_to_validate[start:end]
            before = context[:i - start]
            after = context[i - start + 1:]
            print(f"Context: {before}[{char}]{after}")
            
            # Show progress
            progress = ''.join(validated_chars) + f"[{char}]"
            remaining = text_to_validate[i+1:i+11]
            print(f"Progress: ...{progress[-20:]}...{remaining[:10]}...")
            print(f"📷 Character visualization saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Correct character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    validated_chars.append(char)
                    break
                elif user_input.lower() == "skip":
                    # Skip this character
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_chars.append(char)
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_chars.append(user_input)
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    validated_chars.append('\n')
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_chars)
        print("=" * 60)
        print("✅ True Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    from difflib import SequenceMatcher
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="True Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize true character-by-character OCR
    char_ocr = TrueCharacterOCR()
    
    # Perform character-by-character validation
    validated_text = char_ocr.character_by_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
