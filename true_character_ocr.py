#!/usr/bin/env python3
"""
True Character-by-Character Interactive OCR with Visual Feedback
Forces individual character detection and saves each as char.png
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrueCharacterOCR:
    """True character-by-character OCR with visual feedback"""
    
    def __init__(self):
        self.context_window = 3
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def extract_character_positions(self, image_path: str) -> List[Dict]:
        """Extract character positions using OCR data"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')

        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)

        # Get detailed OCR data with character positions
        data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)

        characters = []
        for i in range(len(data['text'])):
            char = data['text'][i]
            conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0

            if char.strip():  # Only process non-empty characters
                bbox = {
                    'left': data['left'][i],
                    'top': data['top'][i],
                    'width': data['width'][i],
                    'height': data['height'][i]
                }

                characters.append({
                    'char': char,
                    'confidence': conf,
                    'bbox': bbox,
                    'line_num': data['line_num'][i],
                    'word_num': data['word_num'][i]
                })

        return characters

    def estimate_character_position(self, image_path: str, text: str, char_index: int) -> dict:
        """Estimate character position based on text analysis and image dimensions"""
        try:
            # Load image to get dimensions
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Calculate approximate character dimensions
            # Assuming monospace font and typical character spacing
            lines = text.split('\n')

            # Find which line this character is on
            current_pos = 0
            line_num = 0
            char_in_line = char_index

            for i, line in enumerate(lines):
                if current_pos + len(line) + 1 > char_index:  # +1 for newline
                    line_num = i
                    char_in_line = char_index - current_pos
                    break
                current_pos += len(line) + 1

            # Estimate character dimensions based on image size and text content
            avg_chars_per_line = sum(len(line) for line in lines) / len(lines) if lines else 1
            estimated_char_width = img.width / max(avg_chars_per_line, 1)
            estimated_char_height = img.height / max(len(lines), 1)

            # Calculate estimated position
            estimated_x = int(char_in_line * estimated_char_width)
            estimated_y = int(line_num * estimated_char_height)

            return {
                'left': max(0, estimated_x - 5),
                'top': max(0, estimated_y - 5),
                'width': int(estimated_char_width + 10),
                'height': int(estimated_char_height + 10)
            }

        except Exception as e:
            logger.error(f"Failed to estimate character position: {e}")
            return {
                'left': 0,
                'top': 0,
                'width': 50,
                'height': 50
            }

    def create_character_image(self, image_path: str, text: str, char_index: int, char_filename: str = "char.png"):
        """Create a focused character visualization"""
        try:
            if char_index >= len(text):
                return

            char = text[char_index]

            # Load original image
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Try to get OCR character positions first
            char_positions = self.extract_character_positions(image_path)
            char_bbox = None

            # Try to find a matching character position
            if char_positions and char_index < len(char_positions):
                char_bbox = char_positions[char_index]['bbox']

            # If OCR detection failed, estimate position
            if not char_bbox or char_bbox['width'] > img.width * 0.8:  # Too wide, likely whole line
                char_bbox = self.estimate_character_position(image_path, text, char_index)

            # Extract character region with padding
            padding = 5
            left = max(0, char_bbox['left'] - padding)
            top = max(0, char_bbox['top'] - padding)
            right = min(img.width, char_bbox['left'] + char_bbox['width'] + padding)
            bottom = min(img.height, char_bbox['top'] + char_bbox['height'] + padding)

            # Ensure minimum size
            if right - left < 20:
                center_x = (left + right) // 2
                left = max(0, center_x - 15)
                right = min(img.width, center_x + 15)

            if bottom - top < 20:
                center_y = (top + bottom) // 2
                top = max(0, center_y - 15)
                bottom = min(img.height, center_y + 15)

            char_region = img.crop((left, top, right, bottom))

            # Scale up for better visibility
            scale = 15  # Increased scale for better visibility
            new_width = char_region.width * scale
            new_height = char_region.height * scale
            char_region = char_region.resize((new_width, new_height), Image.NEAREST)

            # Create final image with info
            info_height = 120
            final_width = max(new_width + 40, 500)  # Ensure minimum width
            final_height = new_height + info_height + 40

            final_img = Image.new('RGB', (final_width, final_height), 'white')

            # Center the character image
            char_x = (final_width - new_width) // 2
            char_y = 20
            final_img.paste(char_region, (char_x, char_y))

            # Add border around character
            draw = ImageDraw.Draw(final_img)
            draw.rectangle([char_x-3, char_y-3, char_x+new_width+2, char_y+new_height+2],
                         outline='red', width=4)

            # Add info text below
            try:
                font = ImageFont.load_default()
            except:
                font = None

            info_y = char_y + new_height + 20

            # Character info
            draw.text((20, info_y), f"Character: '{char}' (Position {char_index + 1} of {len(text)})", fill='black', font=font)
            draw.text((20, info_y + 20), f"ASCII: {ord(char)} (0x{ord(char):02x}) | Type: {'Letter' if char.isalpha() else 'Digit' if char.isdigit() else 'Symbol'}", fill='blue', font=font)

            # Position info
            draw.text((20, info_y + 40), f"Region: ({left}, {top}) to ({right}, {bottom})", fill='green', font=font)
            draw.text((20, info_y + 60), f"Original size: {right-left}x{bottom-top} | Scaled: {scale}x", fill='green', font=font)

            # Context
            start = max(0, char_index - 5)
            end = min(len(text), char_index + 6)
            context = text[start:end]
            before = context[:char_index - start]
            after = context[char_index - start + 1:]
            context_display = f"{before}[{char}]{after}"
            draw.text((20, info_y + 80), f"Context: {repr(context_display)}", fill='purple', font=font)

            # Add title
            draw.text((20, 5), "Character Extraction", fill='darkblue', font=font)

            # Save the image
            final_img.save(char_filename)
            logger.debug(f"Created character image: {char_filename}")

        except Exception as e:
            logger.error(f"Failed to create character image: {e}")
            # Fallback to simple text visualization
            self._create_fallback_image(text, char_index, char_filename)

    def _create_fallback_image(self, text: str, char_index: int, char_filename: str):
        """Create a simple fallback visualization"""
        try:
            char = text[char_index]
            img = Image.new('RGB', (300, 150), 'white')
            draw = ImageDraw.Draw(img)

            try:
                font = ImageFont.load_default()
            except:
                font = None

            draw.text((50, 50), f"Character: '{char}'", fill='red', font=font)
            draw.text((50, 80), f"Position: {char_index + 1}", fill='blue', font=font)
            draw.text((50, 110), "Fallback mode", fill='gray', font=font)

            img.save(char_filename)
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
    
    def character_by_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform true character-by-character validation"""
        print("🔍 Starting True Character-by-Character OCR Validation")
        print("=" * 60)
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        text_to_validate = '\n'.join(lines)
        
        print(f"📝 Text to validate ({len(text_to_validate)} characters):")
        print(f"Preview: {repr(text_to_validate[:100])}...")
        print()
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining characters")
        print("  - Check char.png for visual feedback of current character")
        print()
        
        validated_chars = []
        corrections_made = 0
        auto_mode = False
        
        for i, char in enumerate(text_to_validate):
            # Create character visualization
            self.create_character_image(image_path, text_to_validate, i, "char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                validated_chars.append(char)
                continue
            
            # Display character info
            print(f"Character {i+1}/{len(text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show context
            start = max(0, i - 10)
            end = min(len(text_to_validate), i + 11)
            context = text_to_validate[start:end]
            before = context[:i - start]
            after = context[i - start + 1:]
            print(f"Context: {before}[{char}]{after}")
            
            # Show progress
            progress = ''.join(validated_chars) + f"[{char}]"
            remaining = text_to_validate[i+1:i+11]
            print(f"Progress: ...{progress[-20:]}...{remaining[:10]}...")
            print(f"📷 Character visualization saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Correct character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    validated_chars.append(char)
                    break
                elif user_input.lower() == "skip":
                    # Skip this character
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_chars.append(char)
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_chars.append(user_input)
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    validated_chars.append('\n')
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_chars)
        print("=" * 60)
        print("✅ True Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    from difflib import SequenceMatcher
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="True Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize true character-by-character OCR
    char_ocr = TrueCharacterOCR()
    
    # Perform character-by-character validation
    validated_text = char_ocr.character_by_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
