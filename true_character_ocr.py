#!/usr/bin/env python3
"""
True Character-by-Character Interactive OCR with Visual Feedback
Forces individual character detection and saves each as char.png
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrueCharacterOCR:
    """True character-by-character OCR with visual feedback"""
    
    def __init__(self):
        self.context_window = 3
    
    def extract_text_as_string(self, image_path: str) -> str:
        """Extract text as a single string using optimal settings"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get text using optimal configuration
        text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        return text.strip()
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def extract_character_positions(self, image_path: str) -> List[Dict]:
        """Extract character positions using OCR data"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')

        # Apply optimal preprocessing
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)

        # Get detailed OCR data with character positions
        data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)

        characters = []
        for i in range(len(data['text'])):
            char = data['text'][i]
            conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0

            if char.strip():  # Only process non-empty characters
                bbox = {
                    'left': data['left'][i],
                    'top': data['top'][i],
                    'width': data['width'][i],
                    'height': data['height'][i]
                }

                characters.append({
                    'char': char,
                    'confidence': conf,
                    'bbox': bbox,
                    'line_num': data['line_num'][i],
                    'word_num': data['word_num'][i]
                })

        return characters

    def create_character_image(self, image_path: str, text: str, char_index: int, char_filename: str = "char.png"):
        """Extract and display the actual character region from the original image"""
        try:
            if char_index >= len(text):
                return

            char = text[char_index]

            # Get character positions from OCR
            char_positions = self.extract_character_positions(image_path)

            # Try to find the corresponding character position
            # This is approximate since we're matching string position to OCR detection
            char_bbox = None

            # Simple approach: try to map text position to OCR character positions
            # This works best for the first few lines where OCR detection is most accurate
            if char_index < len(char_positions):
                char_bbox = char_positions[min(char_index, len(char_positions) - 1)]['bbox']

            # Load original image
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            if char_bbox:
                # Extract character region with padding
                padding = 10
                left = max(0, char_bbox['left'] - padding)
                top = max(0, char_bbox['top'] - padding)
                right = min(img.width, char_bbox['left'] + char_bbox['width'] + padding)
                bottom = min(img.height, char_bbox['top'] + char_bbox['height'] + padding)

                char_region = img.crop((left, top, right, bottom))

                # Scale up for better visibility (10x)
                scale = 10
                new_width = char_region.width * scale
                new_height = char_region.height * scale
                char_region = char_region.resize((new_width, new_height), Image.NEAREST)

                # Create final image with info
                info_height = 100
                final_width = max(new_width, 400)
                final_height = new_height + info_height

                final_img = Image.new('RGB', (final_width, final_height), 'white')

                # Center the character image
                char_x = (final_width - new_width) // 2
                final_img.paste(char_region, (char_x, 0))

                # Add border around character
                draw = ImageDraw.Draw(final_img)
                draw.rectangle([char_x-2, -2, char_x+new_width+1, new_height+1],
                             outline='red', width=3)

                # Add info text below
                try:
                    font = ImageFont.load_default()
                except:
                    font = None

                info_y = new_height + 10
                draw.text((10, info_y), f"Character: '{char}' (Position {char_index + 1})", fill='black', font=font)
                draw.text((10, info_y + 20), f"ASCII: {ord(char)} | Type: {'Letter' if char.isalpha() else 'Digit' if char.isdigit() else 'Symbol'}", fill='blue', font=font)
                draw.text((10, info_y + 40), f"Original size: {char_bbox['width']}x{char_bbox['height']} | Scaled: {scale}x", fill='green', font=font)
                draw.text((10, info_y + 60), f"Confidence: {char_positions[min(char_index, len(char_positions) - 1)].get('confidence', 'N/A')}%", fill='purple', font=font)

            else:
                # Fallback: create text-based visualization if no bbox found
                final_img = Image.new('RGB', (400, 200), 'white')
                draw = ImageDraw.Draw(final_img)

                try:
                    font = ImageFont.load_default()
                except:
                    font = None

                draw.text((50, 50), f"Character: '{char}'", fill='red', font=font)
                draw.text((50, 80), f"Position: {char_index + 1} of {len(text)}", fill='blue', font=font)
                draw.text((50, 110), f"ASCII: {ord(char)} (0x{ord(char):02x})", fill='green', font=font)
                draw.text((50, 140), "Note: Character region not detected", fill='orange', font=font)

            # Save the image
            final_img.save(char_filename)
            logger.debug(f"Created character image: {char_filename}")

        except Exception as e:
            logger.error(f"Failed to create character image: {e}")
            # Fallback to simple text visualization
            self._create_fallback_image(text, char_index, char_filename)

    def _create_fallback_image(self, text: str, char_index: int, char_filename: str):
        """Create a simple fallback visualization"""
        try:
            char = text[char_index]
            img = Image.new('RGB', (300, 150), 'white')
            draw = ImageDraw.Draw(img)

            try:
                font = ImageFont.load_default()
            except:
                font = None

            draw.text((50, 50), f"Character: '{char}'", fill='red', font=font)
            draw.text((50, 80), f"Position: {char_index + 1}", fill='blue', font=font)
            draw.text((50, 110), "Fallback mode", fill='gray', font=font)

            img.save(char_filename)
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
    
    def character_by_character_validation(self, image_path: str, output_file: str = None, max_lines: int = 4) -> str:
        """Perform true character-by-character validation"""
        print("🔍 Starting True Character-by-Character OCR Validation")
        print("=" * 60)
        
        # Extract text as string
        full_text = self.extract_text_as_string(image_path)
        
        if not full_text:
            print("❌ No text detected in image!")
            return ""
        
        # Split into lines and limit to max_lines
        lines = full_text.split('\n')
        if max_lines > 0:
            lines = lines[:max_lines]
        
        # Rejoin limited lines
        text_to_validate = '\n'.join(lines)
        
        print(f"📝 Text to validate ({len(text_to_validate)} characters):")
        print(f"Preview: {repr(text_to_validate[:100])}...")
        print()
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining characters")
        print("  - Check char.png for visual feedback of current character")
        print()
        
        validated_chars = []
        corrections_made = 0
        auto_mode = False
        
        for i, char in enumerate(text_to_validate):
            # Create character visualization
            self.create_character_image(image_path, text_to_validate, i, "char.png")
            
            # Auto-accept in auto mode (except for problematic characters)
            if auto_mode and char.isalnum():
                validated_chars.append(char)
                continue
            
            # Display character info
            print(f"Character {i+1}/{len(text_to_validate)}")
            print(f"Current character: '{char}' (ASCII: {ord(char)})")
            
            # Show context
            start = max(0, i - 10)
            end = min(len(text_to_validate), i + 11)
            context = text_to_validate[start:end]
            before = context[:i - start]
            after = context[i - start + 1:]
            print(f"Context: {before}[{char}]{after}")
            
            # Show progress
            progress = ''.join(validated_chars) + f"[{char}]"
            remaining = text_to_validate[i+1:i+11]
            print(f"Progress: ...{progress[-20:]}...{remaining[:10]}...")
            print(f"📷 Character visualization saved as: char.png")
            
            # Character type info
            char_type = "Letter" if char.isalpha() else "Digit" if char.isdigit() else "Symbol/Space"
            print(f"Type: {char_type}")
            
            # Get user input
            while True:
                user_input = input(f"Correct character ['{char}']: ").strip()
                
                if user_input == "":
                    # Accept the character
                    validated_chars.append(char)
                    break
                elif user_input.lower() == "skip":
                    # Skip this character
                    print("⏭️  Skipped character")
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_chars)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_chars.append(char)
                    print("🤖 Auto mode enabled for alphanumeric characters")
                    break
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_chars.append(user_input)
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '{user_input}'")
                    break
                elif user_input.lower() == "newline" or user_input.lower() == "\\n":
                    # Special case for newline
                    validated_chars.append('\n')
                    corrections_made += 1
                    print(f"✅ Corrected: '{char}' -> '\\n'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', or 'newline'")
            
            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_chars)
        print("=" * 60)
        print("✅ True Character-by-Character Validation Complete!")
        print(f"📊 Characters processed: {len(text_to_validate)}")
        print(f"🔧 Corrections made: {corrections_made}")
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Clean up char.png
        try:
            os.remove("char.png")
            print("🧹 Cleaned up char.png")
        except:
            pass
        
        return final_text

def load_reference_text(reference_file: str, num_lines: int = 4) -> str:
    """Load reference text for comparison"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        reference_lines = lines[:num_lines]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first {num_lines} lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts"""
    from difflib import SequenceMatcher
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def main():
    parser = argparse.ArgumentParser(description="True Character-by-Character Interactive OCR")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to process (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize true character-by-character OCR
    char_ocr = TrueCharacterOCR()
    
    # Perform character-by-character validation
    validated_text = char_ocr.character_by_character_validation(
        args.image_path, 
        args.output, 
        args.lines
    )
    
    if validated_text:
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(repr(validated_text))  # Use repr to show special characters
        print()
        print("Formatted text:")
        print(validated_text)
        
        # Compare with reference if provided
        if args.reference and os.path.exists(args.reference):
            reference_text = load_reference_text(args.reference, args.lines)
            if reference_text:
                similarity = calculate_similarity(validated_text, reference_text)
                print(f"\n📊 Similarity with reference: {similarity:.1%}")
                
                if similarity >= 0.95:
                    print("🎯 Excellent! 95%+ similarity achieved!")
                elif similarity >= 0.90:
                    print("👍 Good! 90%+ similarity achieved!")
                else:
                    print("📈 Room for improvement. Consider more corrections.")
                
                # Show character-by-character differences
                if similarity < 1.0:
                    print("\nCharacter differences:")
                    for i, (ref_char, val_char) in enumerate(zip(reference_text, validated_text)):
                        if ref_char != val_char:
                            print(f"  Position {i}: '{val_char}' should be '{ref_char}'")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
