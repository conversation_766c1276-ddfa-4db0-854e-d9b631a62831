#!/usr/bin/env python3
"""
Final OCR Text Extractor and Validator for RSA Private Key
Focuses on extracting first 4 lines with post-processing and validation
"""

import os
import sys
import time
import json
import argparse
import threading
import multiprocessing
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from difflib import SequenceMatcher
import psutil
import GPUtil
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageEnhance
import pytesseract
import logging
from typing import Dict, List, Any

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemMonitor:
    """Monitor system resources and hardware capabilities"""
    
    def __init__(self):
        self.start_time = time.time()
        self.gpu_available = self._check_gpu_availability()
        
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available and get details"""
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                for gpu in gpus:
                    logger.info(f"GPU Detected: {gpu.name} - Memory: {gpu.memoryTotal}MB")
                    logger.info(f"GPU Load: {gpu.load*100:.1f}% - Memory Used: {gpu.memoryUsed}MB")
                return True
            else:
                logger.info("No GPU detected - using CPU only")
                return False
        except Exception as e:
            logger.warning(f"GPU detection failed: {e}")
            return False
    
    def display_hardware_info(self):
        """Display comprehensive hardware information at startup"""
        logger.info("=" * 60)
        logger.info("HARDWARE CAPABILITY DISPLAY")
        logger.info("=" * 60)
        
        # CPU Information
        cpu_count = multiprocessing.cpu_count()
        cpu_freq = psutil.cpu_freq()
        logger.info(f"CPU Cores: {cpu_count}")
        if cpu_freq:
            logger.info(f"CPU Frequency: {cpu_freq.current:.2f} MHz (Max: {cpu_freq.max:.2f} MHz)")
        
        # Memory Information
        memory = psutil.virtual_memory()
        logger.info(f"Total RAM: {memory.total / (1024**3):.2f} GB")
        logger.info(f"Available RAM: {memory.available / (1024**3):.2f} GB")
        
        # GPU Information
        if self.gpu_available:
            try:
                gpus = GPUtil.getGPUs()
                for i, gpu in enumerate(gpus):
                    logger.info(f"GPU {i}: {gpu.name}")
                    logger.info(f"  Memory: {gpu.memoryTotal}MB (Used: {gpu.memoryUsed}MB)")
                    logger.info(f"  Load: {gpu.load*100:.1f}%")
                    logger.info(f"  Temperature: {gpu.temperature}°C")
            except Exception as e:
                logger.warning(f"GPU info retrieval failed: {e}")
        
        logger.info("=" * 60)
    
    def monitor_resources(self, interval: int = 15):
        """Monitor system resources in real-time"""
        def monitor_loop():
            while True:
                try:
                    # CPU and Memory
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    
                    logger.info(f"[MONITOR] CPU: {cpu_percent:.1f}% | RAM: {memory.percent:.1f}% | "
                              f"Available: {memory.available / (1024**3):.2f}GB")
                    
                    # GPU monitoring if available
                    if self.gpu_available:
                        try:
                            gpus = GPUtil.getGPUs()
                            for i, gpu in enumerate(gpus):
                                logger.info(f"[MONITOR] GPU{i}: {gpu.load*100:.1f}% load | "
                                          f"{gpu.memoryUsed}MB/{gpu.memoryTotal}MB memory | "
                                          f"{gpu.temperature}°C")
                        except Exception as e:
                            logger.warning(f"GPU monitoring failed: {e}")
                    
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Monitoring error: {e}")
                    time.sleep(interval)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        return monitor_thread

def load_reference_text(reference_file: str) -> str:
    """Load and extract first 4 lines from reference PEM file"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        # Extract first 4 lines as specified
        reference_lines = lines[:4]
        reference_text = ''.join(reference_lines).strip()
        
        logger.info(f"Reference text loaded: {len(reference_text)} characters from first 4 lines")
        return reference_text
        
    except Exception as e:
        logger.error(f"Failed to load reference file: {e}")
        return ""

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate similarity between two texts using SequenceMatcher"""
    return SequenceMatcher(None, text1.strip(), text2.strip()).ratio()

def extract_first_4_lines(text: str) -> str:
    """Extract first 4 lines from OCR text"""
    lines = text.split('\n')
    # Take first 4 non-empty lines or first 4 lines including empty ones
    first_4_lines = lines[:4]
    return '\n'.join(first_4_lines).strip()

def post_process_ocr_text(text: str) -> str:
    """Post-process OCR text to fix common errors"""
    # Common OCR corrections for RSA keys
    corrections = [
        # Character substitutions
        ('1', 'I'),  # 1 -> I in some contexts
        ('0', 'O'),  # 0 -> O in some contexts  
        ('5', 'S'),  # 5 -> S
        ('8', 'B'),  # 8 -> B
        ('6', 'G'),  # 6 -> G
        (' ', ''),   # Remove spaces within base64
        ('\t', ''),  # Remove tabs
    ]
    
    processed = text
    
    # Apply corrections to base64 portions (not headers)
    lines = processed.split('\n')
    corrected_lines = []
    
    for line in lines:
        if line.startswith('-----'):
            # Keep headers as-is
            corrected_lines.append(line)
        else:
            # Apply corrections to base64 content
            corrected_line = line
            for old, new in corrections:
                if old == ' ' or old == '\t':
                    # Remove spaces/tabs from base64 lines
                    corrected_line = corrected_line.replace(old, new)
            corrected_lines.append(corrected_line)
    
    return '\n'.join(corrected_lines)

def extract_text_with_config(image_path: str, config: str, preprocessing: str = "none") -> str:
    """Extract text using specific Tesseract configuration and preprocessing"""
    try:
        # Load image with truncated support
        img = Image.open(image_path)
        
        # Convert RGBA to RGB if needed
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply preprocessing if specified
        if preprocessing != "none":
            # Convert to OpenCV format for preprocessing
            cv_img = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            if preprocessing == "basic":
                gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                _, processed = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif preprocessing == "enhanced":
                gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                # Enhance contrast
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                enhanced = clahe.apply(gray)
                # Apply threshold
                _, processed = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif preprocessing == "denoise":
                gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                # Denoise
                denoised = cv2.fastNlMeansDenoising(gray)
                # Apply threshold
                _, processed = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            elif preprocessing == "sharpen":
                gray = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
                # Sharpen
                kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
                sharpened = cv2.filter2D(gray, -1, kernel)
                _, processed = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            else:
                processed = cv2.cvtColor(cv_img, cv2.COLOR_BGR2GRAY)
            
            # Convert back to PIL
            img = Image.fromarray(processed)
        
        # Perform OCR
        text = pytesseract.image_to_string(img, config=config)
        return text.strip()
        
    except Exception as e:
        logger.error(f"OCR extraction failed: {e}")
        return ""

def main():
    parser = argparse.ArgumentParser(description="Final OCR Text Extraction and Validation for RSA Private Key")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--reference", "-r", default="rsa_private_key.pem", 
                       help="Reference file for validation")
    parser.add_argument("--output-dir", "-o", default="ocr_results", 
                       help="Output directory for results")
    parser.add_argument("--similarity-threshold", "-s", type=float, default=0.70,
                       help="Similarity threshold for validation (default: 0.70)")
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize system monitor
    monitor = SystemMonitor()
    monitor.display_hardware_info()
    
    # Start resource monitoring
    monitor.monitor_resources(interval=15)
    
    # Load reference text
    reference_text = load_reference_text(args.reference)
    if not reference_text:
        logger.error("Failed to load reference text. Exiting.")
        return 1
    
    logger.info(f"Starting final OCR processing")
    logger.info(f"Similarity threshold: {args.similarity_threshold * 100}%")
    
    # Define comprehensive configurations
    configurations = [
        {"name": "PSM6_Basic", "config": "--psm 6", "preprocessing": "none"},
        {"name": "PSM6_Enhanced", "config": "--psm 6", "preprocessing": "enhanced"},
        {"name": "PSM6_Denoise", "config": "--psm 6", "preprocessing": "denoise"},
        {"name": "PSM6_Sharpen", "config": "--psm 6", "preprocessing": "sharpen"},
        {"name": "PSM6_Basic_Thresh", "config": "--psm 6", "preprocessing": "basic"},
        {"name": "PSM3_Basic", "config": "--psm 3", "preprocessing": "none"},
        {"name": "PSM3_Enhanced", "config": "--psm 3", "preprocessing": "enhanced"},
        {"name": "PSM3_Denoise", "config": "--psm 3", "preprocessing": "denoise"},
        {"name": "PSM8_Basic", "config": "--psm 8", "preprocessing": "none"},
        {"name": "PSM7_Basic", "config": "--psm 7", "preprocessing": "none"},
        {"name": "PSM11_Basic", "config": "--psm 11", "preprocessing": "none"},
        {"name": "PSM13_Basic", "config": "--psm 13", "preprocessing": "none"},
    ]
    
    results = []
    best_result = None
    best_similarity = 0.0
    
    logger.info(f"Testing {len(configurations)} configurations...")
    
    for i, config in enumerate(configurations):
        logger.info(f"Testing {config['name']} ({i+1}/{len(configurations)})")
        
        start_time = time.time()
        ocr_text = extract_text_with_config(
            args.image_path, 
            config['config'], 
            config['preprocessing']
        )
        processing_time = time.time() - start_time
        
        if ocr_text:
            # Extract first 4 lines for comparison
            first_4_lines = extract_first_4_lines(ocr_text)
            
            # Post-process the text
            processed_text = post_process_ocr_text(first_4_lines)
            
            # Calculate similarity against reference
            similarity = calculate_similarity(processed_text, reference_text)
            
            result = {
                'config_name': config['name'],
                'config_string': config['config'],
                'preprocessing': config['preprocessing'],
                'ocr_text_full': ocr_text,
                'ocr_text_first_4_lines': first_4_lines,
                'ocr_text_processed': processed_text,
                'similarity': similarity,
                'processing_time': processing_time,
                'passes_threshold': similarity >= args.similarity_threshold
            }
            
            results.append(result)
            
            logger.info(f"  Result: {similarity:.3f} similarity ({similarity*100:.1f}%)")
            if len(first_4_lines) > 0:
                logger.info(f"  First 4 lines: {repr(first_4_lines[:100])}")
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_result = result
                
            if similarity >= args.similarity_threshold:
                logger.info(f"  ✓ PASSED threshold!")
        else:
            logger.info(f"  ✗ No text extracted")
    
    # Sort results by similarity
    results.sort(key=lambda x: x['similarity'], reverse=True)
    
    # Save results
    results_file = os.path.join(args.output_dir, 'final_results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to: {results_file}")
    
    # Display summary
    logger.info("=" * 60)
    logger.info("FINAL PROCESSING COMPLETE")
    logger.info("=" * 60)
    
    passing_results = [r for r in results if r['passes_threshold']]
    
    logger.info(f"Total configurations tested: {len(configurations)}")
    logger.info(f"Successful extractions: {len(results)}")
    logger.info(f"Passing {args.similarity_threshold*100}% threshold: {len(passing_results)}")
    logger.info(f"Best similarity achieved: {best_similarity:.3f} ({best_similarity*100:.1f}%)")
    
    if best_result:
        logger.info("=" * 60)
        logger.info("BEST RESULT")
        logger.info("=" * 60)
        logger.info(f"Configuration: {best_result['config_name']}")
        logger.info(f"Similarity: {best_result['similarity']:.3f} ({best_result['similarity']*100:.1f}%)")
        logger.info(f"Preprocessing: {best_result['preprocessing']}")
        logger.info(f"Processing time: {best_result['processing_time']:.2f} seconds")
        
        # Save best result
        best_text_file = os.path.join(args.output_dir, 'best_final_result.txt')
        with open(best_text_file, 'w') as f:
            f.write(best_result['ocr_text_processed'])
        logger.info(f"Best OCR text saved to: {best_text_file}")
        
        # Save full result
        full_text_file = os.path.join(args.output_dir, 'best_final_full_result.txt')
        with open(full_text_file, 'w') as f:
            f.write(best_result['ocr_text_full'])
        logger.info(f"Full OCR text saved to: {full_text_file}")
        
        # Save comparison
        comparison_file = os.path.join(args.output_dir, 'final_comparison.txt')
        with open(comparison_file, 'w') as f:
            f.write("REFERENCE TEXT (first 4 lines):\n")
            f.write("=" * 50 + "\n")
            f.write(reference_text + "\n\n")
            f.write("BEST OCR RESULT (first 4 lines, processed):\n")
            f.write("=" * 50 + "\n")
            f.write(best_result['ocr_text_processed'] + "\n\n")
            f.write("BEST OCR RESULT (first 4 lines, raw):\n")
            f.write("=" * 50 + "\n")
            f.write(best_result['ocr_text_first_4_lines'] + "\n\n")
            f.write(f"SIMILARITY: {best_result['similarity']:.3f} ({best_result['similarity']*100:.1f}%)\n")
            f.write(f"CONFIGURATION: {best_result['config_name']}\n")
        logger.info(f"Comparison saved to: {comparison_file}")
        
        if passing_results:
            logger.info(f"✓ SUCCESS: {len(passing_results)} configuration(s) passed the threshold")
            return 0
        else:
            logger.warning(f"✗ NO RESULTS passed the {args.similarity_threshold*100}% threshold")
            logger.info(f"Consider lowering the threshold. Best result was {best_similarity*100:.1f}%")
            return 1
    else:
        logger.error("No successful OCR results obtained!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
