#!/usr/bin/env python3
"""
Interactive Learning OCR System
Asks for character-by-character validation and learns from corrections
"""

import os
import sys
import json
import argparse
import pickle
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import cv2
import numpy as np
from PIL import Image, ImageFile, ImageDraw, ImageFont
import pytesseract
import logging

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LearningDatabase:
    """Database to store and learn from character corrections"""
    
    def __init__(self, db_file: str = "ocr_learning_db.pkl"):
        self.db_file = db_file
        self.corrections = {}  # {(char_context, ocr_char): correct_char}
        self.confidence_scores = {}  # Track confidence of corrections
        self.load_database()
    
    def load_database(self):
        """Load existing learning database"""
        if os.path.exists(self.db_file):
            try:
                with open(self.db_file, 'rb') as f:
                    data = pickle.load(f)
                    self.corrections = data.get('corrections', {})
                    self.confidence_scores = data.get('confidence_scores', {})
                logger.info(f"Loaded {len(self.corrections)} learned corrections from database")
            except Exception as e:
                logger.warning(f"Could not load database: {e}")
    
    def save_database(self):
        """Save learning database"""
        try:
            data = {
                'corrections': self.corrections,
                'confidence_scores': self.confidence_scores,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.db_file, 'wb') as f:
                pickle.dump(data, f)
            logger.info(f"Saved {len(self.corrections)} corrections to database")
        except Exception as e:
            logger.error(f"Could not save database: {e}")
    
    def add_correction(self, context: str, ocr_char: str, correct_char: str):
        """Add a character correction to the learning database"""
        key = (context, ocr_char)
        
        if key in self.corrections:
            if self.corrections[key] == correct_char:
                # Increase confidence
                self.confidence_scores[key] = self.confidence_scores.get(key, 1) + 1
            else:
                # Conflicting correction, reset confidence
                self.confidence_scores[key] = 1
        else:
            self.confidence_scores[key] = 1
        
        self.corrections[key] = correct_char
        logger.debug(f"Learned: '{ocr_char}' -> '{correct_char}' in context '{context}'")
    
    def get_suggestion(self, context: str, ocr_char: str) -> Optional[str]:
        """Get a suggested correction based on learned patterns"""
        key = (context, ocr_char)
        if key in self.corrections and self.confidence_scores.get(key, 0) >= 2:
            return self.corrections[key]
        return None
    
    def get_stats(self) -> Dict:
        """Get learning statistics"""
        high_confidence = sum(1 for score in self.confidence_scores.values() if score >= 3)
        return {
            'total_corrections': len(self.corrections),
            'high_confidence_corrections': high_confidence,
            'average_confidence': sum(self.confidence_scores.values()) / len(self.confidence_scores) if self.confidence_scores else 0
        }

class InteractiveOCR:
    """Interactive OCR system with character-by-character validation"""
    
    def __init__(self, learning_db: LearningDatabase):
        self.learning_db = learning_db
        self.context_window = 3  # Characters before and after for context
    
    def extract_text_with_confidence(self, image_path: str) -> List[Tuple[str, float, Tuple[int, int, int, int]]]:
        """Extract text with character-level confidence and bounding boxes"""
        # Load and preprocess image
        img = Image.open(image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Apply optimal preprocessing (based on our previous findings)
        img_array = np.array(img)
        processed_img = self._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        # Get detailed OCR data with confidence and positions
        data = pytesseract.image_to_data(pil_img, config="--psm 6 --oem 1", output_type=pytesseract.Output.DICT)
        
        characters = []
        for i in range(len(data['text'])):
            char = data['text'][i]
            conf = int(data['conf'][i])
            
            if char.strip():  # Only process non-empty characters
                bbox = (data['left'][i], data['top'][i], data['width'][i], data['height'][i])
                characters.append((char, conf, bbox))
        
        return characters
    
    def _preprocess_image(self, img_array: np.ndarray) -> np.ndarray:
        """Apply optimal preprocessing based on previous findings"""
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
        else:
            gray = img_array.copy()
        
        # Apply 3x scaling with Lanczos interpolation
        height, width = gray.shape
        new_width = int(width * 3.0)
        new_height = int(height * 3.0)
        scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # Apply manual threshold at 180
        _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
        
        return thresh
    
    def get_character_context(self, characters: List[str], index: int, window: int = None) -> str:
        """Get context around a character for learning"""
        if window is None:
            window = self.context_window

        start = max(0, index - window)
        end = min(len(characters), index + window + 1)
        context_chars = characters[start:end]

        # Replace the target character with a placeholder
        target_pos = index - start
        if 0 <= target_pos < len(context_chars):
            context_chars[target_pos] = '?'

        return ''.join(context_chars)
    
    def create_character_preview(self, image_path: str, bbox: Tuple[int, int, int, int], scale: int = 5) -> str:
        """Create a zoomed preview of a character for better visibility"""
        try:
            img = Image.open(image_path)
            if img.mode == 'RGBA':
                img = img.convert('RGB')

            # Extract character region with some padding
            x, y, w, h = bbox
            padding = 5
            char_region = img.crop((
                max(0, x - padding),
                max(0, y - padding),
                min(img.width, x + w + padding),
                min(img.height, y + h + padding)
            ))

            # Scale up for better visibility
            new_size = (char_region.width * scale, char_region.height * scale)
            char_region = char_region.resize(new_size, Image.NEAREST)

            # Convert to ASCII art for terminal display
            ascii_art = self._image_to_ascii(char_region)
            return ascii_art
        except Exception as e:
            return f"[Preview error: {e}]"

    def _image_to_ascii(self, img: Image.Image, width: int = 20) -> str:
        """Convert image to ASCII art for terminal display"""
        # Convert to grayscale
        img = img.convert('L')

        # Resize to fit terminal width
        aspect_ratio = img.height / img.width
        height = int(width * aspect_ratio * 0.5)  # 0.5 to account for character height
        img = img.resize((width, height))

        # ASCII characters from dark to light
        ascii_chars = "@%#*+=-:. "

        ascii_art = []
        for y in range(img.height):
            line = ""
            for x in range(img.width):
                pixel = img.getpixel((x, y))
                char_index = int(pixel / 255 * (len(ascii_chars) - 1))
                line += ascii_chars[char_index]
            ascii_art.append(line)

        return '\n'.join(ascii_art)

    def interactive_validation(self, image_path: str, output_file: str = None) -> str:
        """Perform interactive character-by-character validation"""
        print("🔍 Starting Interactive OCR Validation")
        print("=" * 50)

        # Extract characters with confidence
        char_data = self.extract_text_with_confidence(image_path)

        if not char_data:
            print("❌ No text detected in image!")
            return ""

        print(f"📝 Detected {len(char_data)} characters")
        print("💡 Instructions:")
        print("  - Press ENTER to accept the character")
        print("  - Type a different character to correct it")
        print("  - Type 'skip' to skip this character")
        print("  - Type 'quit' to finish early")
        print("  - Type 'auto' to auto-accept remaining high-confidence characters")
        print("  - Type 'preview' to see character preview")
        print("  - Type 'context' to see more context")
        print()

        validated_text = []
        corrections_made = 0
        auto_mode = False

        for i, (char, confidence, bbox) in enumerate(char_data):
            # Get context for learning
            current_text = [c[0] for c in char_data[:i]]
            context = self.get_character_context(current_text + [char], len(current_text))

            # Check if we have a learned correction
            suggestion = self.learning_db.get_suggestion(context, char)

            # Auto-accept high confidence characters in auto mode
            if auto_mode and confidence > 80 and not suggestion:
                validated_text.append(char)
                continue

            # Display character info
            print(f"Character {i+1}/{len(char_data)}")
            print(f"OCR detected: '{char}' (confidence: {confidence}%)")
            if suggestion:
                print(f"💡 Learned suggestion: '{suggestion}' (use this? y/n)")
            print(f"Context: '{context}'")

            # Show current progress
            current_progress = ''.join(validated_text) + f"[{char}]" + ''.join([c[0] for c in char_data[i+1:i+6]])
            print(f"Progress: ...{current_progress[-30:]}...")

            # Get user input
            while True:
                if suggestion:
                    user_input = input(f"Correct character ['{char}'] (suggestion: '{suggestion}'): ").strip()
                else:
                    user_input = input(f"Correct character ['{char}']: ").strip()

                if user_input == "":
                    # Accept the character
                    validated_text.append(char)
                    break
                elif user_input.lower() == "y" and suggestion:
                    # Accept the suggestion
                    validated_text.append(suggestion)
                    print(f"✅ Used learned suggestion: '{suggestion}'")
                    break
                elif user_input.lower() == "n" and suggestion:
                    # Reject suggestion, ask for correct character
                    continue
                elif user_input.lower() == "skip":
                    # Skip this character
                    break
                elif user_input.lower() == "quit":
                    # Finish early
                    print("🛑 Validation stopped by user")
                    return ''.join(validated_text)
                elif user_input.lower() == "auto":
                    # Enable auto mode
                    auto_mode = True
                    validated_text.append(char)
                    print("🤖 Auto mode enabled for high-confidence characters")
                    break
                elif user_input.lower() == "preview":
                    # Show character preview
                    preview = self.create_character_preview(image_path, bbox)
                    print("Character preview:")
                    print(preview)
                    continue
                elif user_input.lower() == "context":
                    # Show extended context
                    extended_context = self.get_character_context(current_text + [char], len(current_text), window=10)
                    print(f"Extended context: '{extended_context}'")
                    continue
                elif len(user_input) == 1:
                    # User provided a correction
                    validated_text.append(user_input)
                    corrections_made += 1

                    # Learn from the correction
                    self.learning_db.add_correction(context, char, user_input)
                    print(f"✅ Learned: '{char}' -> '{user_input}' in context '{context}'")
                    break
                else:
                    print("❌ Please enter a single character, 'skip', 'quit', 'auto', 'preview', 'context', or 'y'/'n' for suggestions")

            print()  # Empty line for readability
        
        # Final results
        final_text = ''.join(validated_text)
        print("=" * 50)
        print("✅ Validation Complete!")
        print(f"📊 Characters processed: {len(char_data)}")
        print(f"🔧 Corrections made: {corrections_made}")
        print(f"📚 Learning database now has {len(self.learning_db.corrections)} corrections")
        
        # Save the learning database
        self.learning_db.save_database()
        
        # Save validated text if output file specified
        if output_file:
            with open(output_file, 'w') as f:
                f.write(final_text)
            print(f"💾 Validated text saved to: {output_file}")
        
        # Display learning stats
        stats = self.learning_db.get_stats()
        print(f"📈 Learning Stats:")
        print(f"  - Total corrections: {stats['total_corrections']}")
        print(f"  - High confidence: {stats['high_confidence_corrections']}")
        print(f"  - Average confidence: {stats['average_confidence']:.1f}")
        
        return final_text

def apply_learned_corrections(text: str, learning_db: LearningDatabase, context_window: int = 3) -> str:
    """Apply learned corrections to text automatically"""
    corrected_text = list(text)
    corrections_applied = 0
    
    for i, char in enumerate(text):
        # Get context
        start = max(0, i - context_window)
        end = min(len(text), i + context_window + 1)
        context_chars = list(text[start:end])
        
        # Replace target character with placeholder
        target_pos = i - start
        if 0 <= target_pos < len(context_chars):
            context_chars[target_pos] = '?'
        
        context = ''.join(context_chars)
        
        # Check for learned correction
        suggestion = learning_db.get_suggestion(context, char)
        if suggestion and suggestion != char:
            corrected_text[i] = suggestion
            corrections_applied += 1
    
    logger.info(f"Applied {corrections_applied} learned corrections")
    return ''.join(corrected_text)

def main():
    parser = argparse.ArgumentParser(description="Interactive Learning OCR System")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", "-o", help="Output file for validated text")
    parser.add_argument("--db-file", "-d", default="ocr_learning_db.pkl", 
                       help="Learning database file")
    parser.add_argument("--auto-correct", "-a", action="store_true",
                       help="Apply learned corrections automatically (no interaction)")
    parser.add_argument("--stats", "-s", action="store_true",
                       help="Show learning database statistics")
    
    args = parser.parse_args()
    
    # Initialize learning database
    learning_db = LearningDatabase(args.db_file)
    
    # Show stats if requested
    if args.stats:
        stats = learning_db.get_stats()
        print("📊 Learning Database Statistics:")
        print(f"  - Total corrections: {stats['total_corrections']}")
        print(f"  - High confidence: {stats['high_confidence_corrections']}")
        print(f"  - Average confidence: {stats['average_confidence']:.1f}")
        return 0
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    # Initialize interactive OCR
    interactive_ocr = InteractiveOCR(learning_db)
    
    if args.auto_correct:
        # Auto-correct mode using learned corrections
        print("🤖 Auto-correction mode using learned patterns")
        
        # First get basic OCR
        img = Image.open(args.image_path)
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        img_array = np.array(img)
        processed_img = interactive_ocr._preprocess_image(img_array)
        pil_img = Image.fromarray(processed_img)
        
        basic_text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
        
        # Apply learned corrections
        corrected_text = apply_learned_corrections(basic_text, learning_db)
        
        print("Original OCR:")
        print(basic_text)
        print("\nAfter learned corrections:")
        print(corrected_text)
        
        if args.output:
            with open(args.output, 'w') as f:
                f.write(corrected_text)
            print(f"💾 Corrected text saved to: {args.output}")
    else:
        # Interactive validation mode
        validated_text = interactive_ocr.interactive_validation(args.image_path, args.output)
        
        print("\n📄 Final validated text:")
        print("=" * 50)
        print(validated_text)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
