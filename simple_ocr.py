#!/usr/bin/env python3
"""
Simple OCR Script using pytesseract and OpenCV
"""

import argparse
import cv2
import pytesseract
from PIL import Image
import numpy as np
import os
import sys


def preprocess_image(image_path):
    """
    Preprocess image to improve OCR accuracy
    """
    try:
        # Try reading with PIL first (better PNG support)
        pil_img = Image.open(image_path)
        # Convert to RGB if it's RGBA (remove alpha channel)
        if pil_img.mode == 'RGBA':
            pil_img = pil_img.convert('RGB')
        # Convert PIL to numpy array for OpenCV
        import numpy as np
        img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    except Exception as e:
        print(f"PIL reading failed, trying OpenCV: {e}")
        # Fallback to OpenCV
        img = cv2.imread(image_path)
        
    if img is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Apply threshold to get better contrast
    _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    return thresh


def extract_text_simple(image_path, preprocess=True):
    """
    Extract text from image using simple pytesseract
    """
    try:
        if preprocess:
            # Preprocess the image
            processed_img = preprocess_image(image_path)
            # Convert back to PIL Image for pytesseract
            pil_img = Image.fromarray(processed_img)
        else:
            # Use original image with PIL only
            pil_img = Image.open(image_path)
            # Convert RGBA to RGB if needed
            if pil_img.mode == 'RGBA':
                pil_img = pil_img.convert('RGB')
        
        # Extract text using pytesseract
        text = pytesseract.image_to_string(pil_img)
        return text.strip()
        
    except Exception as e:
        print(f"Error processing image: {e}")
        # Try with just PIL and no preprocessing
        try:
            print("Trying fallback method with PIL only...")
            pil_img = Image.open(image_path)
            if pil_img.mode == 'RGBA':
                pil_img = pil_img.convert('RGB')
            text = pytesseract.image_to_string(pil_img)
            return text.strip()
        except Exception as e2:
            print(f"Fallback method also failed: {e2}")
            return None


def extract_text_with_config(image_path, config="--psm 6"):
    """
    Extract text with custom tesseract configuration
    """
    try:
        # Preprocess the image
        processed_img = preprocess_image(image_path)
        pil_img = Image.fromarray(processed_img)
        
        # Extract text with custom config
        text = pytesseract.image_to_string(pil_img, config=config)
        return text.strip()
        
    except Exception as e:
        print(f"Error processing image with config: {e}")
        # Try with just PIL and no preprocessing
        try:
            print("Trying fallback method with PIL only...")
            pil_img = Image.open(image_path)
            if pil_img.mode == 'RGBA':
                pil_img = pil_img.convert('RGB')
            text = pytesseract.image_to_string(pil_img, config=config)
            return text.strip()
        except Exception as e2:
            print(f"Fallback method also failed: {e2}")
            return None


def main():
    parser = argparse.ArgumentParser(description="Simple OCR tool for extracting text from images")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--no-preprocess", action="store_true", help="Skip image preprocessing")
    parser.add_argument("--config", default="--psm 6", help="Tesseract configuration (default: --psm 6)")
    parser.add_argument("--output", "-o", help="Output file to save extracted text")
    
    args = parser.parse_args()
    
    # Check if image file exists
    if not os.path.exists(args.image_path):
        print(f"Error: Image file '{args.image_path}' not found")
        sys.exit(1)
    
    print(f"Processing image: {args.image_path}")
    
    # Extract text using simple method
    print("\n=== Simple OCR ===")
    text_simple = extract_text_simple(args.image_path, preprocess=not args.no_preprocess)
    if text_simple:
        print(text_simple)
    else:
        print("No text found or error occurred")
    
    # Extract text with custom config
    print(f"\n=== OCR with config: {args.config} ===")
    text_config = extract_text_with_config(args.image_path, args.config)
    if text_config:
        print(text_config)
    else:
        print("No text found or error occurred")
    
    # Save to output file if specified
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write("=== Simple OCR Results ===\n")
                f.write(text_simple or "No text found")
                f.write(f"\n\n=== OCR with config: {args.config} ===\n")
                f.write(text_config or "No text found")
            print(f"\nResults saved to: {args.output}")
        except Exception as e:
            print(f"Error saving to file: {e}")


if __name__ == "__main__":
    main()
