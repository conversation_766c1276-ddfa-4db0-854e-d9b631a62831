#!/usr/bin/env python3
"""
Key Processing Script for CTF Challenge
Cleans up the OCR output and processes the RSA key
"""

import re
import base64
import argparse
import sys
from pathlib import Path

def clean_ocr_text(text):
    """
    Clean up OCR artifacts and format the key properly
    """
    # Remove newlines and extra spaces
    cleaned = re.sub(r'\s+', '', text)
    
    # OCR often misreads some characters, try common fixes
    replacements = {
        ' ': '',
        'l': '1',  # lowercase l often misread as 1
        'O': '0',  # uppercase O often misread as 0
        '/02': '/0',  # Common OCR error
        'bACB': 'wACB',  # Common OCR error
        'FAOQ': 'FAOQ',  # Keep as is, these might be correct
    }
    
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    
    return cleaned

def format_as_pem_key(base64_text, key_type="PRIVATE"):
    """
    Format base64 text as a proper PEM key
    """
    # Clean the base64 text
    b64_clean = re.sub(r'[^A-Za-z0-9+/=]', '', base64_text)
    
    # Add line breaks every 64 characters
    lines = []
    for i in range(0, len(b64_clean), 64):
        lines.append(b64_clean[i:i+64])
    
    # Create PEM format
    pem_lines = [f"-----BEGIN RSA {key_type} KEY-----"]
    pem_lines.extend(lines)
    pem_lines.append(f"-----END RSA {key_type} KEY-----")
    
    return '\n'.join(pem_lines)

def validate_base64(text):
    """
    Check if the text is valid base64
    """
    try:
        # Remove whitespace and check if it's valid base64
        clean_text = re.sub(r'[^A-Za-z0-9+/=]', '', text)
        base64.b64decode(clean_text)
        return True, clean_text
    except Exception as e:
        return False, str(e)

def main():
    parser = argparse.ArgumentParser(description="Process OCR'd RSA key")
    parser.add_argument("input_file", help="Input file containing OCR'd key")
    parser.add_argument("--output", "-o", help="Output file for cleaned key")
    parser.add_argument("--validate", "-v", action="store_true", help="Validate base64 encoding")
    
    args = parser.parse_args()
    
    # Read the input file
    if not Path(args.input_file).exists():
        print(f"Error: File {args.input_file} not found")
        sys.exit(1)
    
    with open(args.input_file, 'r') as f:
        content = f.read()
    
    # Extract just the base64 part (skip headers and metadata)
    lines = content.split('\n')
    base64_lines = []
    
    # Look for the actual base64 content
    capturing = False
    for line in lines:
        line = line.strip()
        if 'PIL Recovery:' in line or '---------------' in line:
            capturing = True
            continue
        elif capturing and line and not line.startswith('==='):
            base64_lines.append(line)
        elif capturing and line.startswith('==='):
            break
    
    if not base64_lines:
        print("No base64 content found in the file")
        sys.exit(1)
    
    # Join all base64 lines
    raw_base64 = ''.join(base64_lines)
    
    print(f"Extracted {len(raw_base64)} characters of base64 data")
    
    # Clean the text
    cleaned_base64 = clean_ocr_text(raw_base64)
    print(f"After cleaning: {len(cleaned_base64)} characters")
    
    # Validate if requested
    if args.validate:
        is_valid, result = validate_base64(cleaned_base64)
        if is_valid:
            print("✓ Base64 validation passed")
        else:
            print(f"✗ Base64 validation failed: {result}")
            print("Trying some common OCR corrections...")
            
            # Try some automatic corrections
            corrections = [
                (r'[Il1|]', '1'),  # Common OCR errors
                (r'[O0o]', '0'),
                (r'[S5$]', 'S'),
                (r'[Z2]', 'Z'),
            ]
            
            for pattern, replacement in corrections:
                test_text = re.sub(pattern, replacement, cleaned_base64)
                is_valid, _ = validate_base64(test_text)
                if is_valid:
                    print(f"✓ Correction successful with pattern {pattern} -> {replacement}")
                    cleaned_base64 = test_text
                    break
    
    # Format as PEM
    pem_key = format_as_pem_key(cleaned_base64)
    
    # Output results
    if args.output:
        with open(args.output, 'w') as f:
            f.write(pem_key)
        print(f"Cleaned key saved to: {args.output}")
    else:
        print("\nCleaned PEM Key:")
        print("=" * 50)
        print(pem_key)
    
    # Also save raw cleaned base64
    raw_output = args.output.replace('.pem', '_raw.txt') if args.output else 'cleaned_key_raw.txt'
    with open(raw_output, 'w') as f:
        f.write(cleaned_base64)
    print(f"Raw base64 saved to: {raw_output}")

if __name__ == "__main__":
    main()
