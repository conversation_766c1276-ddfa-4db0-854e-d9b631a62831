#!/usr/bin/env python3
"""
Simple RSA Key Extractor
Uses the best configuration found during comprehensive testing
"""

import sys
import argparse
from PIL import Image, ImageFile
import pytesseract

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

def extract_rsa_key(image_path: str, lines: int = 4) -> str:
    """
    Extract RSA private key from image using optimal configuration
    
    Args:
        image_path: Path to the image file
        lines: Number of lines to extract (default: 4)
    
    Returns:
        Extracted text as string
    """
    try:
        # Load image with truncated support
        img = Image.open(image_path)
        
        # Convert RGBA to RGB if needed
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        # Use the best configuration found: PSM 6 with no preprocessing
        text = pytesseract.image_to_string(img, config="--psm 6")
        
        # Extract specified number of lines
        text_lines = text.split('\n')
        extracted_lines = text_lines[:lines]
        
        return '\n'.join(extracted_lines).strip()
        
    except Exception as e:
        print(f"Error extracting text: {e}")
        return ""

def main():
    parser = argparse.ArgumentParser(description="Simple RSA Key Extractor")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--lines", "-l", type=int, default=4, 
                       help="Number of lines to extract (default: 4)")
    parser.add_argument("--output", "-o", help="Output file (optional)")
    
    args = parser.parse_args()
    
    print(f"Extracting RSA key from: {args.image_path}")
    print(f"Lines to extract: {args.lines}")
    
    # Extract text
    extracted_text = extract_rsa_key(args.image_path, args.lines)
    
    if extracted_text:
        print("\nExtracted text:")
        print("=" * 50)
        print(extracted_text)
        print("=" * 50)
        
        # Save to file if specified
        if args.output:
            with open(args.output, 'w') as f:
                f.write(extracted_text)
            print(f"\nText saved to: {args.output}")
        
        return 0
    else:
        print("Failed to extract text from image")
        return 1

if __name__ == "__main__":
    sys.exit(main())
