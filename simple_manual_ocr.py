#!/usr/bin/env python3
"""
Simple Manual OCR Validation
Line-by-line validation with learning capabilities
"""

import os
import sys
import json
import argparse
from difflib import SequenceMatcher
import cv2
import numpy as np
from PIL import Image, ImageFile
import pytesseract

# Enable loading of truncated images
ImageFile.LOAD_TRUNCATED_IMAGES = True

def extract_text_lines(image_path: str) -> list:
    """Extract text lines from image"""
    # Load and preprocess image (using our best settings)
    img = Image.open(image_path)
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    
    img_array = np.array(img)
    
    # Apply optimal preprocessing
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)
    else:
        gray = img_array.copy()
    
    # Apply 3x scaling with Lanczos interpolation
    height, width = gray.shape
    new_width = int(width * 3.0)
    new_height = int(height * 3.0)
    scaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
    
    # Apply manual threshold at 180
    _, thresh = cv2.threshold(scaled, 180, 255, cv2.THRESH_BINARY)
    
    # Convert back to PIL
    pil_img = Image.fromarray(thresh)
    
    # Extract text
    text = pytesseract.image_to_string(pil_img, config="--psm 6 --oem 1")
    
    # Split into lines and clean up
    lines = text.split('\n')
    cleaned_lines = []
    for line in lines:
        cleaned_line = line.strip()
        if cleaned_line:  # Only keep non-empty lines
            cleaned_lines.append(cleaned_line)
    
    return cleaned_lines

def load_reference_lines(reference_file: str, num_lines: int = 4) -> list:
    """Load reference lines from file"""
    try:
        with open(reference_file, 'r') as f:
            lines = f.readlines()
        
        # Get first num_lines and clean them
        reference_lines = []
        for i in range(min(num_lines, len(lines))):
            reference_lines.append(lines[i].strip())
        
        return reference_lines
    except Exception as e:
        print(f"Error loading reference: {e}")
        return []

def calculate_line_similarity(line1: str, line2: str) -> float:
    """Calculate similarity between two lines"""
    return SequenceMatcher(None, line1, line2).ratio()

def manual_line_validation(ocr_lines: list, reference_lines: list) -> list:
    """Manually validate OCR lines against reference"""
    print("🔍 Manual Line-by-Line OCR Validation")
    print("=" * 60)
    print()
    print("Instructions:")
    print("  - Press ENTER to accept the line")
    print("  - Type corrected text to fix the line")
    print("  - Type 'skip' to skip this line")
    print("  - Type 'quit' to finish early")
    print("  - Type 'ref' to see the reference line")
    print()
    
    validated_lines = []
    
    for i, ocr_line in enumerate(ocr_lines):
        print(f"Line {i+1}/{len(ocr_lines)}")
        print("-" * 40)
        
        # Show reference if available
        if i < len(reference_lines):
            ref_line = reference_lines[i]
            similarity = calculate_line_similarity(ocr_line, ref_line)
            print(f"Reference:  '{ref_line}'")
            print(f"OCR result: '{ocr_line}'")
            print(f"Similarity: {similarity:.1%}")
        else:
            print(f"OCR result: '{ocr_line}'")
            print("(No reference available)")
        
        print()
        
        while True:
            user_input = input("Correct line (ENTER to accept): ").strip()
            
            if user_input == "":
                # Accept the line
                validated_lines.append(ocr_line)
                print(f"✅ Accepted: '{ocr_line}'")
                break
            elif user_input.lower() == "skip":
                print("⏭️  Skipped line")
                break
            elif user_input.lower() == "quit":
                print("🛑 Validation stopped by user")
                return validated_lines
            elif user_input.lower() == "ref" and i < len(reference_lines):
                print(f"📖 Reference: '{reference_lines[i]}'")
                continue
            else:
                # User provided a correction
                validated_lines.append(user_input)
                print(f"✅ Corrected: '{user_input}'")
                
                # Show what changed
                if user_input != ocr_line:
                    print(f"   Changed from: '{ocr_line}'")
                break
        
        print()
    
    return validated_lines

def save_results(validated_lines: list, output_file: str, reference_lines: list = None):
    """Save validation results"""
    # Save validated text
    with open(output_file, 'w') as f:
        for line in validated_lines:
            f.write(line + '\n')
    
    print(f"💾 Validated text saved to: {output_file}")
    
    # Save comparison report
    report_file = output_file.replace('.txt', '_report.txt')
    with open(report_file, 'w') as f:
        f.write("OCR VALIDATION REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("VALIDATED LINES:\n")
        f.write("-" * 20 + "\n")
        for i, line in enumerate(validated_lines):
            f.write(f"Line {i+1}: {line}\n")
        
        if reference_lines:
            f.write("\nCOMPARISON WITH REFERENCE:\n")
            f.write("-" * 30 + "\n")
            
            total_similarity = 0
            valid_comparisons = 0
            
            for i in range(max(len(validated_lines), len(reference_lines))):
                if i < len(validated_lines) and i < len(reference_lines):
                    val_line = validated_lines[i]
                    ref_line = reference_lines[i]
                    similarity = calculate_line_similarity(val_line, ref_line)
                    total_similarity += similarity
                    valid_comparisons += 1
                    
                    f.write(f"\nLine {i+1}:\n")
                    f.write(f"  Validated: {val_line}\n")
                    f.write(f"  Reference: {ref_line}\n")
                    f.write(f"  Similarity: {similarity:.1%}\n")
                elif i < len(validated_lines):
                    f.write(f"\nLine {i+1}: {validated_lines[i]} (no reference)\n")
                elif i < len(reference_lines):
                    f.write(f"\nLine {i+1}: (missing) vs reference: {reference_lines[i]}\n")
            
            if valid_comparisons > 0:
                avg_similarity = total_similarity / valid_comparisons
                f.write(f"\nOVERALL SIMILARITY: {avg_similarity:.1%}\n")
    
    print(f"📊 Validation report saved to: {report_file}")

def main():
    parser = argparse.ArgumentParser(description="Simple Manual OCR Validation")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--reference", "-r", help="Reference file for comparison")
    parser.add_argument("--output", "-o", default="validated_text.txt", 
                       help="Output file for validated text")
    parser.add_argument("--lines", "-l", type=int, default=4,
                       help="Number of lines to validate (default: 4)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ Image file not found: {args.image_path}")
        return 1
    
    print("🚀 Starting Simple Manual OCR Validation")
    print("=" * 50)
    
    # Extract text from image
    print("📸 Extracting text from image...")
    ocr_lines = extract_text_lines(args.image_path)
    
    if not ocr_lines:
        print("❌ No text detected in image!")
        return 1
    
    print(f"📝 Detected {len(ocr_lines)} lines")
    
    # Load reference if provided
    reference_lines = []
    if args.reference:
        if os.path.exists(args.reference):
            reference_lines = load_reference_lines(args.reference, args.lines)
            print(f"📖 Loaded {len(reference_lines)} reference lines")
        else:
            print(f"⚠️  Reference file not found: {args.reference}")
    
    # Limit to specified number of lines
    if len(ocr_lines) > args.lines:
        ocr_lines = ocr_lines[:args.lines]
        print(f"📏 Limited to first {args.lines} lines")
    
    print()
    
    # Perform manual validation
    validated_lines = manual_line_validation(ocr_lines, reference_lines)
    
    if validated_lines:
        print("=" * 50)
        print("✅ Validation Complete!")
        print(f"📊 Validated {len(validated_lines)} lines")
        
        # Save results
        save_results(validated_lines, args.output, reference_lines)
        
        print("\n📄 Final validated text:")
        print("-" * 30)
        for i, line in enumerate(validated_lines):
            print(f"{i+1}: {line}")
    else:
        print("❌ No lines were validated")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
